#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    print(f"正在安装 {package}...")
    try:
        # 使用subprocess.call避免PowerShell解析问题
        result = subprocess.call([
            sys.executable, "-m", "pip", "install", package
        ])
        if result == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败")
            return False
    except Exception as e:
        print(f"❌ 安装 {package} 时出错: {e}")
        return False

def main():
    print("🔧 闲鱼依赖安装")
    print("=" * 30)
    
    packages = ["execjs", "blackboxprotobuf"]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 30)
    if success_count == len(packages):
        print("🎉 所有依赖安装完成！")
        print("现在可以使用完整的闲鱼同步功能了。")
    else:
        print(f"⚠️ {len(packages) - success_count} 个包安装失败")
        print("不过没关系，系统会使用模拟数据模式。")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
