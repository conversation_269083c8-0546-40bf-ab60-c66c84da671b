import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// 管理员登录
export const adminLogin = createAsyncThunk(
  'admin/login',
  async ({ username, password }, { rejectWithValue }) => {
    try {
      const response = await axios.post('/api/auth/admin/login', { username, password });
      const { token, admin } = response.data.data;
      
      // 保存令牌到本地存储
      localStorage.setItem('adminToken', token);
      
      // 不再设置全局请求头
      // axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      return { token, admin };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '登录失败，请检查用户名和密码');
    }
  }
);

// 检查管理员登录状态
export const checkAdminStatus = createAsyncThunk(
  'admin/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      
      if (!token) {
        return rejectWithValue('未登录');
      }
      
      // 不再设置全局请求头
      // axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // 获取统计数据作为验证（因为我们没有专门的验证令牌接口）
      await axios.get('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      return { isAuthenticated: true };
    } catch (error) {
      localStorage.removeItem('adminToken');
      return rejectWithValue('会话已过期');
    }
  }
);

// 管理员退出
export const adminLogout = createAsyncThunk(
  'admin/logout',
  async () => {
    localStorage.removeItem('adminToken');
    // 不再修改全局请求头
    // delete axios.defaults.headers.common['Authorization'];
    return { isAuthenticated: false };
  }
);

// 生成卡密
export const generateKeys = createAsyncThunk(
  'admin/generateKeys',
  async ({ count, expireDays, prefix, keyType, maxDevices }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post('/api/admin/card-keys/generate', 
        { count, expireDays, prefix, keyType, maxDevices },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '生成卡密失败');
    }
  }
);

// 获取卡密列表
export const getCardKeys = createAsyncThunk(
  'admin/getCardKeys',
  async ({ page = 1, limit = 10, is_used, status } = {}, { rejectWithValue }) => {
    try {
      let url = `/api/admin/card-keys?page=${page}&limit=${limit}`;
      if (is_used !== undefined) url += `&is_used=${is_used}`;
      if (status !== undefined) url += `&status=${status}`;
      
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取卡密列表失败');
    }
  }
);

// 更新卡密状态
export const updateCardKeyStatus = createAsyncThunk(
  'admin/updateCardKeyStatus',
  async ({ id, status }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.put(
        `/api/admin/card-keys/${id}/status`,
        { status },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return response.data.data.cardKey;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '更新卡密状态失败');
    }
  }
);

// 删除卡密
export const deleteCardKey = createAsyncThunk(
  'admin/deleteCardKey',
  async (id, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`/api/admin/card-keys/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return { id, message: response.data.message };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '删除卡密失败');
    }
  }
);

// 批量删除卡密
export const batchDeleteCardKeys = createAsyncThunk(
  'admin/batchDeleteCardKeys',
  async (ids, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/card-keys/batch-delete',
        { ids },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return { ids, deleted: response.data.data.deleted, message: response.data.message };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '批量删除卡密失败');
    }
  }
);

// 清理过期卡密
export const cleanupExpiredCardKeys = createAsyncThunk(
  'admin/cleanupExpiredCardKeys',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/card-keys/cleanup-expired',
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return { 
        deletedCount: response.data.data.deletedCount, 
        message: response.data.message 
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '清理过期卡密失败');
    }
  }
);

// 清理已使用卡密
export const cleanupUsedCardKeys = createAsyncThunk(
  'admin/cleanupUsedCardKeys',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/card-keys/cleanup-used',
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return { 
        deletedCount: response.data.data.deletedCount, 
        message: response.data.message 
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '清理已使用卡密失败');
    }
  }
);

// 清理所有无效卡密（已使用、已过期、状态为无效的）
export const cleanupInvalidCardKeys = createAsyncThunk(
  'admin/cleanupInvalidCardKeys',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/card-keys/cleanup-invalid',
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return { 
        deletedCount: response.data.data.deletedCount, 
        message: response.data.message 
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '清理无效卡密失败');
    }
  }
);

// 获取系统统计数据
export const getSystemStats = createAsyncThunk(
  'admin/getSystemStats',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取统计数据失败');
    }
  }
);

// 获取热门文件排行榜
export const getPopularFiles = createAsyncThunk(
  'admin/getPopularFiles',
  async ({ limit = 10, days = 30 } = {}, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`/api/admin/analytics/popular-files?limit=${limit}&days=${days}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取热门文件排行榜失败');
    }
  }
);

// 获取详细数据分析
export const getDetailedAnalytics = createAsyncThunk(
  'admin/getDetailedAnalytics',
  async ({ days = 30 } = {}, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`/api/admin/analytics/detailed?days=${days}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取详细数据分析失败');
    }
  }
);

// 获取管理员文件列表
export const getAdminFiles = createAsyncThunk(
  'admin/getAdminFiles',
  async ({ category_id, keyword, page = 1, limit = 10 } = {}, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      
      if (!token) {
        return rejectWithValue('未登录或会话已过期');
      }
      
      let url = `/api/admin/files?page=${page}&limit=${limit}`;
      if (category_id) url += `&category_id=${category_id}`;
      if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;

      console.log('正在请求管理员文件列表:', url);

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // 添加数据验证逻辑
      const responseData = response.data;
      if (!responseData || !responseData.data) {
        console.error('API返回数据格式不正确:', responseData);
        return {
          files: [],
          total: 0,
          page: page,
          limit: limit
        };
      }
      
      // 确保数据结构完整
      const data = responseData.data;
      return {
        files: data.files || [],
        total: data.total || 0,
        page: data.page || page,
        limit: data.limit || limit
      };
    } catch (error) {
      console.error('获取管理员文件列表失败:', error);
      // 返回一个默认的数据结构，避免前端解析错误
      return rejectWithValue({
        message: error.response?.data?.message || '获取文件列表失败',
        defaultData: {
          files: [],
          total: 0,
          page: page,
          limit: limit
        }
      });
    }
  }
);

// 获取管理员分类列表
export const getAdminCategories = createAsyncThunk(
  'admin/getAdminCategories',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get('/api/admin/categories', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data.categories;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取分类列表失败');
    }
  }
);

// 修改管理员密码
export const changeAdminPassword = createAsyncThunk(
  'admin/changePassword',
  async ({ currentPassword, newPassword, confirmPassword }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/change-password',
        { currentPassword, newPassword, confirmPassword },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '修改密码失败');
    }
  }
);

// 获取提取记录
export const getExtractionRecords = createAsyncThunk(
  'admin/getExtractionRecords',
  async ({ page = 1, limit = 5 } = {}, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`/api/admin/extraction-records?page=${page}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // 添加数据验证
      const responseData = response.data;
      if (!responseData || !responseData.data) {
        console.error('API返回数据格式不正确:', responseData);
        return {
          records: [],
          total: 0,
          page: page,
          limit: limit
        };
      }
      
      const data = responseData.data;
      return {
        records: data.records || [],
        total: data.total || 0,
        page: data.page || page,
        limit: data.limit || limit
      };
    } catch (error) {
      console.error('获取提取记录失败:', error);
      return rejectWithValue({
        message: error.response?.data?.message || '获取提取记录失败',
        defaultData: {
          records: [],
          total: 0,
          page: page,
          limit: limit
        }
      });
    }
  }
);

// 删除提取记录
export const deleteExtractionRecord = createAsyncThunk(
  'admin/deleteExtractionRecord',
  async (id, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`/api/admin/extraction-records/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return { id, message: response.data.message };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '删除提取记录失败');
    }
  }
);

// 批量删除提取记录
export const batchDeleteExtractionRecords = createAsyncThunk(
  'admin/batchDeleteExtractionRecords',
  async (ids, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(
        '/api/admin/extraction-records/batch-delete',
        { ids },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      return { ids, deleted: response.data.data.deleted, message: response.data.message };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '批量删除提取记录失败');
    }
  }
);

// 同步闲鱼商品
export const syncXianyuItems = createAsyncThunk(
  'admin/syncXianyuItems',
  async ({ cookies, maxItems, getDetails }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post('/api/admin/xianyu/sync', {
        cookies,
        maxItems,
        getDetails
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '同步闲鱼商品失败');
    }
  }
);

// 获取闲鱼商品列表
export const getXianyuItems = createAsyncThunk(
  'admin/getXianyuItems',
  async ({ page = 1, limit = 10, keyword } = {}, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      let url = `/api/admin/xianyu/items?page=${page}&limit=${limit}`;
      if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '获取闲鱼商品列表失败');
    }
  }
);

// 删除闲鱼商品
export const deleteXianyuItem = createAsyncThunk(
  'admin/deleteXianyuItem',
  async (id, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('adminToken');
      await axios.delete(`/api/admin/xianyu/items/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || '删除闲鱼商品失败');
    }
  }
);

const adminSlice = createSlice({
  name: 'admin',
  initialState: {
    isAuthenticated: false,
    admin: null,
    loading: false,
    error: null,
    
    // 密码修改状态
    changingPassword: false,
    passwordChanged: false,
    passwordError: null,
    
    // 卡密相关
    cardKeys: [],
    generatedKeys: [],
    totalKeys: 0,
    keyPage: 1,
    keyLimit: 10,
    generatingKeys: false,
    loadingKeys: false,
    selectedRowKeys: [],
    
    // 文件相关
    files: [],
    totalFiles: 0,
    filePage: 1,
    fileLimit: 10,
    loadingFiles: false,
    
    // 提取记录相关
    extractionRecords: [],
    totalExtractionRecords: 0,
    extractionRecordPage: 1,
    extractionRecordLimit: 5,
    loadingExtractionRecords: false,
    
    // 分类相关
    adminCategories: [],
    loadingAdminCategories: false,
    
    // 统计数据
    stats: null,
    loadingStats: false,

    // 数据分析相关
    popularFiles: null,
    loadingPopularFiles: false,
    detailedAnalytics: null,
    loadingDetailedAnalytics: false,

    // 闲鱼商品相关
    xianyuItems: [],
    totalXianyuItems: 0,
    xianyuPage: 1,
    xianyuLimit: 10,
    loadingXianyuItems: false,
    syncingXianyuItems: false,
    xianyuSyncProgress: null,
    xianyuSyncResult: null
  },
  reducers: {
    clearAdminError: (state) => {
      state.error = null;
    },
    clearGeneratedKeys: (state) => {
      state.generatedKeys = [];
    },
    clearPasswordState: (state) => {
      state.passwordChanged = false;
      state.passwordError = null;
    },
    setKeyPage: (state, action) => {
      state.keyPage = action.payload;
    },
    setSelectedRowKeys: (state, action) => {
      state.selectedRowKeys = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // 管理员登录
      .addCase(adminLogin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(adminLogin.fulfilled, (state, action) => {
        state.isAuthenticated = true;
        state.admin = action.payload.admin;
        state.loading = false;
      })
      .addCase(adminLogin.rejected, (state, action) => {
        state.isAuthenticated = false;
        state.admin = null;
        state.loading = false;
        state.error = action.payload;
      })
      
      // 检查管理员登录状态
      .addCase(checkAdminStatus.fulfilled, (state, action) => {
        state.isAuthenticated = action.payload.isAuthenticated;
      })
      .addCase(checkAdminStatus.rejected, (state) => {
        state.isAuthenticated = false;
        state.admin = null;
      })
      
      // 管理员退出
      .addCase(adminLogout.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.admin = null;
      })
      
      // 生成卡密
      .addCase(generateKeys.pending, (state) => {
        state.generatingKeys = true;
        state.error = null;
      })
      .addCase(generateKeys.fulfilled, (state, action) => {
        state.generatedKeys = action.payload.keys;
        state.generatingKeys = false;
      })
      .addCase(generateKeys.rejected, (state, action) => {
        state.generatingKeys = false;
        state.error = action.payload;
      })
      
      // 获取卡密列表
      .addCase(getCardKeys.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(getCardKeys.fulfilled, (state, action) => {
        state.cardKeys = action.payload.keys;
        state.totalKeys = action.payload.total;
        state.keyPage = action.payload.page;
        state.keyLimit = action.payload.limit;
        state.loadingKeys = false;
      })
      .addCase(getCardKeys.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })
      
      // 获取系统统计数据
      .addCase(getSystemStats.pending, (state) => {
        state.loadingStats = true;
      })
      .addCase(getSystemStats.fulfilled, (state, action) => {
        state.stats = action.payload;
        state.loadingStats = false;
      })
      .addCase(getSystemStats.rejected, (state, action) => {
        state.loadingStats = false;
        state.error = action.payload;
      })
      
      // 获取管理员文件列表
      .addCase(getAdminFiles.pending, (state) => {
        state.loadingFiles = true;
        state.error = null;
      })
      .addCase(getAdminFiles.fulfilled, (state, action) => {
        // 安全地处理数据
        state.files = action.payload.files || [];
        state.totalFiles = action.payload.total || 0;
        state.filePage = action.payload.page || 1;
        state.fileLimit = action.payload.limit || 10;
        state.loadingFiles = false;
      })
      .addCase(getAdminFiles.rejected, (state, action) => {
        state.loadingFiles = false;
        state.error = action.payload?.message || '获取文件列表失败';
        
        // 如果请求被拒绝，使用默认数据避免界面错误
        if (action.payload?.defaultData) {
          state.files = action.payload.defaultData.files || [];
          state.totalFiles = action.payload.defaultData.total || 0;
          state.filePage = action.payload.defaultData.page || 1;
          state.fileLimit = action.payload.defaultData.limit || 10;
        }
      })
      
      // 获取管理员分类列表
      .addCase(getAdminCategories.pending, (state) => {
        state.loadingAdminCategories = true;
        state.error = null;
      })
      .addCase(getAdminCategories.fulfilled, (state, action) => {
        state.adminCategories = action.payload;
        state.loadingAdminCategories = false;
      })
      .addCase(getAdminCategories.rejected, (state, action) => {
        state.loadingAdminCategories = false;
        state.error = action.payload;
      })
      
      // 修改管理员密码
      .addCase(changeAdminPassword.pending, (state) => {
        state.changingPassword = true;
        state.passwordChanged = false;
        state.passwordError = null;
      })
      .addCase(changeAdminPassword.fulfilled, (state) => {
        state.changingPassword = false;
        state.passwordChanged = true;
      })
      .addCase(changeAdminPassword.rejected, (state, action) => {
        state.changingPassword = false;
        state.passwordError = action.payload;
      })
      
      // 更新卡密状态
      .addCase(updateCardKeyStatus.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(updateCardKeyStatus.fulfilled, (state, action) => {
        state.loadingKeys = false;
        // 更新列表中的卡密状态
        if (state.cardKeys) {
          const index = state.cardKeys.findIndex(key => key.id === action.payload.id);
          if (index !== -1) {
            state.cardKeys[index] = action.payload;
          }
        }
      })
      .addCase(updateCardKeyStatus.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload || '更新卡密状态失败';
      })
      
      // 删除卡密
      .addCase(deleteCardKey.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(deleteCardKey.fulfilled, (state, action) => {
        state.loadingKeys = false;
        state.cardKeys = state.cardKeys.filter(key => key.id !== action.payload.id);
        state.totalKeys -= 1;
      })
      .addCase(deleteCardKey.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })
      
      // 批量删除卡密
      .addCase(batchDeleteCardKeys.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(batchDeleteCardKeys.fulfilled, (state, action) => {
        state.loadingKeys = false;
        state.cardKeys = state.cardKeys.filter(key => !action.payload.ids.includes(key.id));
        state.totalKeys -= action.payload.deleted;
        // 清空选中的行
        state.selectedRowKeys = [];
      })
      .addCase(batchDeleteCardKeys.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })
      
      // 清理过期卡密
      .addCase(cleanupExpiredCardKeys.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(cleanupExpiredCardKeys.fulfilled, (state, action) => {
        state.loadingKeys = false;
        // 清理成功后，重新获取卡密列表以更新状态
        getCardKeys({ page: 1, limit: 10, is_used: false, status: 'active' });
      })
      .addCase(cleanupExpiredCardKeys.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })
      
      // 清理已使用卡密
      .addCase(cleanupUsedCardKeys.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(cleanupUsedCardKeys.fulfilled, (state, action) => {
        state.loadingKeys = false;
        // 清理成功后，重新获取卡密列表以更新状态
        getCardKeys({ page: 1, limit: 10 });
      })
      .addCase(cleanupUsedCardKeys.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })

      // 清理所有无效卡密
      .addCase(cleanupInvalidCardKeys.pending, (state) => {
        state.loadingKeys = true;
        state.error = null;
      })
      .addCase(cleanupInvalidCardKeys.fulfilled, (state, action) => {
        state.loadingKeys = false;
        // 清理成功后，重新获取卡密列表以更新状态
        getCardKeys({ page: 1, limit: 10, is_used: false, status: 'active' });
      })
      .addCase(cleanupInvalidCardKeys.rejected, (state, action) => {
        state.loadingKeys = false;
        state.error = action.payload;
      })

      
      // 获取提取记录
      .addCase(getExtractionRecords.pending, (state) => {
        state.loadingExtractionRecords = true;
        state.error = null;
      })
      .addCase(getExtractionRecords.fulfilled, (state, action) => {
        // 安全地处理数据
        state.extractionRecords = action.payload.records || [];
        state.totalExtractionRecords = action.payload.total || 0;
        state.extractionRecordPage = action.payload.page || 1;
        state.extractionRecordLimit = action.payload.limit || 5;
        state.loadingExtractionRecords = false;
      })
      .addCase(getExtractionRecords.rejected, (state, action) => {
        state.loadingExtractionRecords = false;
        state.error = action.payload?.message || '获取提取记录失败';
        
        // 如果请求被拒绝，使用默认数据避免界面错误
        if (action.payload?.defaultData) {
          state.extractionRecords = action.payload.defaultData.records || [];
          state.totalExtractionRecords = action.payload.defaultData.total || 0;
          state.extractionRecordPage = action.payload.defaultData.page || 1;
          state.extractionRecordLimit = action.payload.defaultData.limit || 5;
        }
      })
      
      // 删除提取记录
      .addCase(deleteExtractionRecord.pending, (state) => {
        state.loadingExtractionRecords = true;
        state.error = null;
      })
      .addCase(deleteExtractionRecord.fulfilled, (state, action) => {
        state.loadingExtractionRecords = false;
        state.extractionRecords = state.extractionRecords.filter(record => record.id !== action.payload.id);
        state.totalExtractionRecords -= 1;
      })
      .addCase(deleteExtractionRecord.rejected, (state, action) => {
        state.loadingExtractionRecords = false;
        state.error = action.payload;
      })
      
      // 批量删除提取记录
      .addCase(batchDeleteExtractionRecords.pending, (state) => {
        state.loadingExtractionRecords = true;
        state.error = null;
      })
      .addCase(batchDeleteExtractionRecords.fulfilled, (state, action) => {
        state.loadingExtractionRecords = false;
        state.extractionRecords = state.extractionRecords.filter(record => !action.payload.ids.includes(record.id));
        state.totalExtractionRecords -= action.payload.deleted;
      })
      .addCase(batchDeleteExtractionRecords.rejected, (state, action) => {
        state.loadingExtractionRecords = false;
        state.error = action.payload;
      })

      // 获取热门文件排行榜
      .addCase(getPopularFiles.pending, (state) => {
        state.loadingPopularFiles = true;
        state.error = null;
      })
      .addCase(getPopularFiles.fulfilled, (state, action) => {
        state.popularFiles = action.payload;
        state.loadingPopularFiles = false;
      })
      .addCase(getPopularFiles.rejected, (state, action) => {
        state.loadingPopularFiles = false;
        state.error = action.payload;
      })

      // 获取详细数据分析
      .addCase(getDetailedAnalytics.pending, (state) => {
        state.loadingDetailedAnalytics = true;
        state.error = null;
      })
      .addCase(getDetailedAnalytics.fulfilled, (state, action) => {
        state.detailedAnalytics = action.payload;
        state.loadingDetailedAnalytics = false;
      })
      .addCase(getDetailedAnalytics.rejected, (state, action) => {
        state.loadingDetailedAnalytics = false;
        state.error = action.payload;
      })

      // 同步闲鱼商品
      .addCase(syncXianyuItems.pending, (state) => {
        state.syncingXianyuItems = true;
        state.error = null;
        state.xianyuSyncProgress = null;
        state.xianyuSyncResult = null;
      })
      .addCase(syncXianyuItems.fulfilled, (state, action) => {
        state.syncingXianyuItems = false;
        state.xianyuSyncResult = action.payload;
      })
      .addCase(syncXianyuItems.rejected, (state, action) => {
        state.syncingXianyuItems = false;
        state.error = action.payload;
      })

      // 获取闲鱼商品列表
      .addCase(getXianyuItems.pending, (state) => {
        state.loadingXianyuItems = true;
        state.error = null;
      })
      .addCase(getXianyuItems.fulfilled, (state, action) => {
        state.xianyuItems = action.payload.items;
        state.totalXianyuItems = action.payload.total;
        state.xianyuPage = action.payload.page;
        state.xianyuLimit = action.payload.limit;
        state.loadingXianyuItems = false;
      })
      .addCase(getXianyuItems.rejected, (state, action) => {
        state.loadingXianyuItems = false;
        state.error = action.payload;
      })

      // 删除闲鱼商品
      .addCase(deleteXianyuItem.fulfilled, (state, action) => {
        state.xianyuItems = state.xianyuItems.filter(item => item.id !== action.payload);
        state.totalXianyuItems -= 1;
      })
      .addCase(deleteXianyuItem.rejected, (state, action) => {
        state.error = action.payload;
      });
  }
});

export const { 
  clearAdminError, 
  clearGeneratedKeys,
  clearPasswordState,
  setKeyPage,
  setSelectedRowKeys
} = adminSlice.actions;

export default adminSlice.reducer; 