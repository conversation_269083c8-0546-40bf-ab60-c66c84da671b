# 闲鱼商品同步功能使用说明

## 功能概述

本功能可以将闲鱼商品信息同步到系统的files表中，实现以下映射：
- 闲鱼商品ID → files表的important_text字段
- 闲鱼商品标题 → files表的file_name字段
- 闲鱼商品描述 → files表的description字段
- 闲鱼商品图片链接 → files表的preview_images字段

## 使用步骤

### 1. 获取闲鱼Cookie

1. 打开浏览器，访问 https://www.goofish.com
2. 登录您的闲鱼账号
3. 按F12打开开发者工具
4. 切换到Network标签页
5. 刷新页面或点击任意链接
6. 在请求列表中找到任意一个请求
7. 在Request Headers中找到Cookie字段
8. 复制完整的Cookie值

### 2. 在管理员页面同步商品

1. 登录管理员后台
2. 进入"文件管理"页面
3. 点击"同步闲鱼商品"按钮
4. 在弹出的对话框中：
   - 粘贴刚才复制的Cookie
   - 设置最大同步数量（0表示全部）
   - 选择是否获取详细信息
5. 点击"开始同步"

### 3. 查看同步结果

同步完成后，您可以在文件列表中看到：
- 文件类型为"xianyu"的记录
- 文件名为商品标题
- 描述为商品详细描述
- 简介图片为商品图片

## 注意事项

1. **Cookie有效期**：Cookie会过期，如果同步失败请重新获取
2. **请求频率**：为避免被限制，系统会在每个商品间添加1秒延迟
3. **数据去重**：相同商品ID的商品会被更新而不是重复创建
4. **图片处理**：最多保存前5张商品图片
5. **网络环境**：确保服务器能够访问闲鱼API

## 技术实现

### 后端组件
- `server/controllers/xianyuController.js` - 闲鱼同步控制器
- `server/scripts/sync_xianyu_items.py` - Python同步脚本
- `server/routes/adminRoutes.js` - 添加了闲鱼相关路由

### 前端组件
- `client/src/pages/admin/Files.js` - 添加了同步按钮和模态框
- `client/src/store/slices/adminSlice.js` - 添加了闲鱼相关状态管理

### 数据库映射
```
闲鱼商品 → files表
├── 商品ID → important_text
├── 商品标题 → file_name
├── 商品描述 → description
├── 商品图片 → preview_images (JSON格式)
├── 文件类型 → file_type = 'xianyu'
└── 状态 → status = 1
```

## 故障排除

### 常见错误

1. **Cookie解析失败**
   - 确保复制了完整的Cookie字符串
   - 检查Cookie是否包含必要的字段（_m_h5_tk, unb等）

2. **Python脚本执行失败**
   - 确保服务器安装了Python 3
   - 检查XianYu目录下的依赖文件是否完整

3. **网络请求失败**
   - 检查服务器网络连接
   - 确认闲鱼API是否可访问

4. **数据库保存失败**
   - 检查数据库连接
   - 确认files表结构正确

### 调试方法

1. 查看浏览器控制台错误信息
2. 检查服务器日志
3. 使用测试脚本验证Python环境：
   ```bash
   cd server/scripts
   python test_xianyu_sync.py
   ```

## 更新日志

- 2025-08-02: 初始版本发布
  - 实现基本的闲鱼商品同步功能
  - 支持商品信息映射到files表
  - 添加管理员界面操作
