#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼功能依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装 {package} 失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"❌ {package} 未安装")
        return False

def main():
    print("🔧 闲鱼功能依赖检查和安装")
    print("=" * 50)
    
    # 需要安装的包
    required_packages = [
        'execjs',
        'blackboxprotobuf'
    ]
    
    # 检查当前安装状态
    print("\n📋 检查当前依赖状态:")
    missing_packages = []
    
    for package in required_packages:
        if not check_package(package):
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 所有依赖都已安装！")
        return
    
    # 安装缺失的包
    print(f"\n📦 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    print("\n🚀 开始安装...")
    
    success_count = 0
    for package in missing_packages:
        print(f"\n正在安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {success_count}/{len(missing_packages)} 成功")
    
    if success_count == len(missing_packages):
        print("🎉 所有依赖安装完成！")
        print("\n📝 接下来您可以:")
        print("1. 重新尝试同步闲鱼商品")
        print("2. 如果仍有问题，请检查JavaScript文件是否存在")
    else:
        print("⚠️  部分依赖安装失败")
        print("\n🔧 手动安装命令:")
        for package in missing_packages:
            print(f"  pip install {package}")
        print("\n💡 如果安装失败，可以使用简化版本（会显示模拟数据）")

if __name__ == "__main__":
    main()
