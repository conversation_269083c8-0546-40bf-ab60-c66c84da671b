#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试闲鱼同步功能
"""

import json
import sys

def test_sync():
    """测试同步功能"""
    try:
        # 模拟成功的同步结果
        result = {
            'success': True,
            'message': '测试同步成功！获取到 3 个商品',
            'data': {
                'user_info': {
                    'user_id': 'test_user_123',
                    'device_id': 'test_device_456'
                },
                'items': [
                    {
                        'xianyu_item_id': '123456789',
                        'file_name': '测试商品1 - iPhone 15 Pro Max',
                        'description': '这是一个测试商品的详细描述，包含了商品的各种特性和功能介绍。',
                        'preview_images': json.dumps([
                            'https://example.com/image1.jpg',
                            'https://example.com/image2.jpg'
                        ]),
                        'file_type': 'xianyu',
                        'file_path': None,
                        'file_size': None,
                        'important_text': '123456789',
                        'status': 1,
                        'category_id': None,
                        'original_data': {
                            'price': '¥8999',
                            'originalPrice': '¥9999',
                            'status': '1',
                            'statusText': '在售',
                            'viewCount': 156,
                            'likeCount': 23,
                            'publishTime': '2025-01-15 10:30:00',
                            'location': '北京市',
                            'sellerId': 'seller123',
                            'sellerNick': '测试卖家'
                        }
                    },
                    {
                        'xianyu_item_id': '987654321',
                        'file_name': '测试商品2 - MacBook Pro 16寸',
                        'description': '另一个测试商品的描述，展示了不同类型商品的信息格式。',
                        'preview_images': json.dumps([
                            'https://example.com/image3.jpg'
                        ]),
                        'file_type': 'xianyu',
                        'file_path': None,
                        'file_size': None,
                        'important_text': '987654321',
                        'status': 1,
                        'category_id': None,
                        'original_data': {
                            'price': '¥15999',
                            'originalPrice': '¥18999',
                            'status': '1',
                            'statusText': '在售',
                            'viewCount': 89,
                            'likeCount': 12,
                            'publishTime': '2025-01-14 15:20:00',
                            'location': '上海市',
                            'sellerId': 'seller123',
                            'sellerNick': '测试卖家'
                        }
                    },
                    {
                        'xianyu_item_id': '456789123',
                        'file_name': '测试商品3 - AirPods Pro 2代',
                        'description': '第三个测试商品，用于验证多商品同步功能。',
                        'preview_images': None,
                        'file_type': 'xianyu',
                        'file_path': None,
                        'file_size': None,
                        'important_text': '456789123',
                        'status': 1,
                        'category_id': None,
                        'original_data': {
                            'price': '¥1299',
                            'originalPrice': '¥1599',
                            'status': '1',
                            'statusText': '在售',
                            'viewCount': 234,
                            'likeCount': 45,
                            'publishTime': '2025-01-13 09:15:00',
                            'location': '广州市',
                            'sellerId': 'seller123',
                            'sellerNick': '测试卖家'
                        }
                    }
                ],
                'total': 3
            }
        }
        
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        error_result = {
            'success': False,
            'message': f'测试脚本执行错误: {str(e)}',
            'data': None
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == '__main__':
    test_sync()
