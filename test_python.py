#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python环境和依赖
"""

import sys
import subprocess

def test_python_env():
    print("🔍 Python环境检测")
    print("=" * 40)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()

def test_pip():
    print("📦 测试pip功能")
    print("=" * 40)
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip可用: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ pip不可用: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ pip测试失败: {e}")
        return False

def test_packages():
    print("\n🔧 检测依赖包")
    print("=" * 40)
    
    packages = ['execjs', 'blackboxprotobuf']
    missing = []
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing.append(package)
    
    return missing

def install_packages(packages):
    print(f"\n📥 安装缺失的包: {', '.join(packages)}")
    print("=" * 40)
    
    for package in packages:
        print(f"正在安装 {package}...")
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")
        except Exception as e:
            print(f"❌ {package} 安装异常: {e}")

def main():
    print("🚀 闲鱼功能环境检测和安装")
    print("=" * 50)
    
    # 测试Python环境
    test_python_env()
    
    # 测试pip
    if not test_pip():
        print("\n❌ pip不可用，无法自动安装依赖")
        print("💡 建议：手动下载依赖包或使用模拟模式")
        return
    
    # 检测依赖包
    missing = test_packages()
    
    if not missing:
        print("\n🎉 所有依赖都已安装，可以使用完整功能！")
        return
    
    # 询问是否安装
    print(f"\n❓ 发现 {len(missing)} 个缺失的包，是否现在安装？")
    print("输入 y 安装，输入 n 跳过（将使用模拟模式）")
    
    try:
        choice = input("请选择 (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            install_packages(missing)
            print("\n🔄 重新检测...")
            missing_after = test_packages()
            if not missing_after:
                print("\n🎉 所有依赖安装完成！")
            else:
                print(f"\n⚠️ 仍有 {len(missing_after)} 个包未成功安装")
        else:
            print("\n💡 跳过安装，将使用模拟模式")
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
