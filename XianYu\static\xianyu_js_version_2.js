// 闲鱼JavaScript工具函数
// 用于生成签名和设备ID等

function generate_mid() {
    // 生成随机的mid
    var chars = '0123456789abcdef';
    var result = '';
    for (var i = 0; i < 32; i++) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
}

function generate_uuid() {
    // 生成UUID格式的字符串
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0;
        var v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

function generate_device_id(user_id) {
    // 基于用户ID生成设备ID
    if (!user_id) {
        user_id = 'default_user';
    }
    
    // 简单的哈希函数
    var hash = 0;
    for (var i = 0; i < user_id.length; i++) {
        var char = user_id.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    
    // 转换为正数并格式化
    hash = Math.abs(hash);
    var deviceId = hash.toString(16).padStart(16, '0');
    
    return deviceId;
}

function generate_sign(t, token, data) {
    // 生成签名
    // 这是一个简化版本的签名生成函数
    var crypto = require('crypto');
    
    try {
        var signStr = token + '&' + t + '&' + '34839810' + '&' + data;
        var hash = crypto.createHash('md5').update(signStr).digest('hex');
        return hash;
    } catch (e) {
        // 如果crypto不可用，使用简单的哈希
        var hash = 0;
        var str = token + t + data;
        for (var i = 0; i < str.length; i++) {
            var char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    }
}

// 导出函数（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generate_mid: generate_mid,
        generate_uuid: generate_uuid,
        generate_device_id: generate_device_id,
        generate_sign: generate_sign
    };
}
