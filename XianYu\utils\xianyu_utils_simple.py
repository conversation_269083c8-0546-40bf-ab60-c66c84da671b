import hashlib
import time
import random
import uuid

def trans_cookies(cookies_str):
    """解析Cookie字符串为字典"""
    cookies = dict()
    for i in cookies_str.split("; "):
        try:
            cookies[i.split('=')[0]] = '='.join(i.split('=')[1:])
        except:
            continue
    return cookies

def generate_mid():
    """生成随机的mid"""
    chars = '0123456789abcdef'
    return ''.join(random.choice(chars) for _ in range(32))

def generate_uuid():
    """生成UUID"""
    return str(uuid.uuid4())

def generate_device_id(user_id):
    """基于用户ID生成设备ID"""
    if not user_id:
        user_id = 'default_user'
    
    # 使用MD5哈希生成设备ID
    hash_obj = hashlib.md5(user_id.encode('utf-8'))
    device_id = hash_obj.hexdigest()[:16]
    return device_id

def generate_sign(t, token, data):
    """生成签名"""
    try:
        # 构建签名字符串
        sign_str = f"{token}&{t}&34839810&{data}"
        
        # 生成MD5哈希
        hash_obj = hashlib.md5(sign_str.encode('utf-8'))
        return hash_obj.hexdigest()
    except Exception as e:
        # 如果出错，返回一个简单的哈希
        simple_str = str(token) + str(t) + str(data)
        hash_obj = hashlib.md5(simple_str.encode('utf-8'))
        return hash_obj.hexdigest()

def get_timestamp():
    """获取当前时间戳（毫秒）"""
    return str(int(time.time() * 1000))

def generate_random_string(length=16):
    """生成随机字符串"""
    chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    return ''.join(random.choice(chars) for _ in range(length))
