#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版闲鱼客户端
当缺少复杂依赖时使用此版本
"""

import json
import time
from typing import List, Dict, Optional
from utils.xianyu_utils_simple import trans_cookies, generate_device_id


class XianyuClientSimple:
    """简化版闲鱼客户端"""
    
    def __init__(self, cookies_str: str):
        """
        初始化客户端
        
        Args:
            cookies_str: 闲鱼网站的Cookie字符串
        """
        self.cookies_str = cookies_str
        self.cookies = None
        self.device_id = None
        self.user_id = None
        
        # 初始化认证信息
        self._init_auth()
    
    def _init_auth(self):
        """初始化认证信息"""
        try:
            self.cookies = trans_cookies(self.cookies_str)
            self.user_id = self.cookies.get('unb', 'default_user')
            self.device_id = generate_device_id(self.user_id)
        except Exception as e:
            raise ValueError(f"Cookie解析失败: {e}")
    
    def get_all_items(self,
                     seller_id: Optional[str] = None,
                     get_details: bool = True,
                     max_items: int = 0,
                     delay: float = 1.0,
                     progress_callback: Optional[callable] = None) -> List[Dict]:
        """
        获取所有商品信息（模拟数据）
        
        Args:
            seller_id: 卖家ID，None表示获取自己的商品
            get_details: 是否获取详细信息
            max_items: 最大获取数量，0表示全部
            delay: 每个商品详情获取的延迟时间（秒）
            progress_callback: 进度回调函数
            
        Returns:
            商品信息列表
        """
        # 由于缺少依赖，返回模拟数据
        mock_items = [
            {
                'itemId': '123456789',
                'title': '【模拟数据】iPhone 15 Pro Max 256GB',
                'description': '这是一个模拟的商品数据，用于测试同步功能。实际使用时请安装完整的Python依赖。',
                'fullDescription': '完整的商品描述：这是一个模拟的iPhone商品，包含了详细的产品信息和使用说明。由于缺少必要的Python依赖包，当前显示的是模拟数据。',
                'price': '¥8999',
                'originalPrice': '¥9999',
                'images': [
                    'https://example.com/iphone1.jpg',
                    'https://example.com/iphone2.jpg'
                ],
                'detailImages': [
                    'https://example.com/iphone1.jpg',
                    'https://example.com/iphone2.jpg',
                    'https://example.com/iphone3.jpg'
                ],
                'mainImage': 'https://example.com/iphone1.jpg',
                'status': '1',
                'statusText': '在售',
                'viewCount': 156,
                'likeCount': 23,
                'publishTime': '2025-01-15 10:30:00',
                'location': '北京市',
                'sellerId': 'seller123',
                'sellerNick': '测试卖家',
                'hasDetailDesc': True,
                'descImproved': True
            },
            {
                'itemId': '987654321',
                'title': '【模拟数据】MacBook Pro 16寸 M3 Max',
                'description': '另一个模拟商品数据，展示不同类型的商品信息。',
                'fullDescription': '完整描述：这是一台高性能的MacBook Pro，配备M3 Max芯片。这是模拟数据，实际使用请安装Python依赖。',
                'price': '¥25999',
                'originalPrice': '¥28999',
                'images': [
                    'https://example.com/macbook1.jpg'
                ],
                'detailImages': [
                    'https://example.com/macbook1.jpg',
                    'https://example.com/macbook2.jpg'
                ],
                'mainImage': 'https://example.com/macbook1.jpg',
                'status': '1',
                'statusText': '在售',
                'viewCount': 89,
                'likeCount': 12,
                'publishTime': '2025-01-14 15:20:00',
                'location': '上海市',
                'sellerId': 'seller123',
                'sellerNick': '测试卖家',
                'hasDetailDesc': True,
                'descImproved': True
            }
        ]
        
        # 限制数量
        if max_items > 0:
            mock_items = mock_items[:max_items]
        
        # 模拟进度回调
        if progress_callback:
            for i, item in enumerate(mock_items, 1):
                progress_callback(i, len(mock_items), item)
                time.sleep(delay)
        
        return mock_items
    
    def get_simple_items(self, 
                        seller_id: Optional[str] = None,
                        max_items: int = 0) -> List[Dict]:
        """
        快速获取商品基本信息
        
        Args:
            seller_id: 卖家ID
            max_items: 最大获取数量
            
        Returns:
            商品基本信息列表
        """
        return self.get_all_items(
            seller_id=seller_id,
            get_details=False,
            max_items=max_items
        )
    
    def search_items(self, 
                    keyword: str,
                    items: Optional[List[Dict]] = None) -> List[Dict]:
        """
        搜索商品
        
        Args:
            keyword: 搜索关键词
            items: 商品列表
            
        Returns:
            匹配的商品列表
        """
        if items is None:
            items = self.get_simple_items()
        
        keyword = keyword.lower()
        matched_items = []
        
        for item in items:
            title = item.get('title', '').lower()
            desc = item.get('description', '').lower()
            full_desc = item.get('fullDescription', '').lower()
            
            if (keyword in title or 
                keyword in desc or 
                keyword in full_desc):
                matched_items.append(item)
        
        return matched_items
    
    def get_user_info(self) -> Dict:
        """获取当前用户信息"""
        return {
            'user_id': self.user_id,
            'device_id': self.device_id
        }
