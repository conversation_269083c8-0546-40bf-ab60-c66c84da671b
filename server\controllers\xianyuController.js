const { spawn } = require('child_process');
const path = require('path');
const File = require('../models/File');
const { Op } = require('sequelize');

/**
 * 同步闲鱼商品到files表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const syncXianyuItems = async (req, res) => {
  try {
    const { cookies, maxItems = 0, getDetails = true } = req.body;

    if (!cookies) {
      return res.status(400).json({
        success: false,
        message: 'Cookie不能为空'
      });
    }

    // 设置响应头支持流式传输
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // 构建Python脚本路径
    const scriptPath = path.join(__dirname, '../scripts/sync_xianyu_items.py');
    
    // 准备参数
    const config = JSON.stringify({
      cookies,
      maxItems: parseInt(maxItems) || 0,
      getDetails: getDetails !== false
    });

    // 启动Python进程 - 尝试不同的Python命令
    let pythonCmd = 'python3';
    try {
      // 在Windows上可能需要使用python而不是python3
      if (process.platform === 'win32') {
        pythonCmd = 'python';
      }
    } catch (e) {
      pythonCmd = 'python';
    }

    const pythonProcess = spawn(pythonCmd, [scriptPath, config], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let outputData = '';
    let errorData = '';
    let progressSent = false;

    // 处理Python进程输出
    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      outputData += output;

      // 检查是否是进度信息
      const lines = output.split('\n').filter(line => line.trim());
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);
          if (parsed.type === 'progress') {
            // 发送进度信息
            if (!progressSent) {
              res.write(JSON.stringify({
                type: 'progress',
                current: parsed.current,
                total: parsed.total,
                item_title: parsed.item_title
              }) + '\n');
            }
          }
        } catch (e) {
          // 不是JSON格式，继续处理
        }
      }
    });

    pythonProcess.stderr.on('data', (data) => {
      errorData += data.toString();
    });

    pythonProcess.on('close', async (code) => {
      try {
        if (code !== 0) {
          return res.status(500).json({
            success: false,
            message: `Python脚本执行失败: ${errorData || '未知错误'}`
          });
        }

        // 解析Python脚本输出
        const lines = outputData.split('\n').filter(line => line.trim());
        let result = null;

        // 找到最后一个有效的JSON结果（排除进度信息）
        for (let i = lines.length - 1; i >= 0; i--) {
          try {
            const parsed = JSON.parse(lines[i]);
            if (parsed.type !== 'progress') {
              result = parsed;
              break;
            }
          } catch (e) {
            continue;
          }
        }

        if (!result) {
          return res.status(500).json({
            success: false,
            message: '无法解析Python脚本输出'
          });
        }

        if (!result.success) {
          return res.status(400).json(result);
        }

        // 保存商品到数据库
        const items = result.data.items || [];
        const savedItems = [];
        const errors = [];

        for (const item of items) {
          try {
            // 检查是否已存在相同的闲鱼商品ID
            const existingFile = await File.findOne({
              where: {
                important_text: item.xianyu_item_id,
                file_type: 'xianyu'
              }
            });

            if (existingFile) {
              // 更新现有记录
              await existingFile.update({
                file_name: item.file_name,
                description: item.description,
                preview_images: item.preview_images,
                upload_time: new Date()
              });
              savedItems.push({
                id: existingFile.id,
                action: 'updated',
                xianyu_item_id: item.xianyu_item_id,
                title: item.file_name
              });
            } else {
              // 创建新记录
              const newFile = await File.create({
                file_name: item.file_name,
                file_path: item.file_path,
                file_size: item.file_size,
                file_type: item.file_type,
                description: item.description,
                important_text: item.important_text,
                preview_images: item.preview_images,
                status: item.status,
                category_id: item.category_id
              });
              savedItems.push({
                id: newFile.id,
                action: 'created',
                xianyu_item_id: item.xianyu_item_id,
                title: item.file_name
              });
            }
          } catch (error) {
            errors.push({
              xianyu_item_id: item.xianyu_item_id,
              title: item.file_name,
              error: error.message
            });
          }
        }

        // 返回最终结果
        res.json({
          success: true,
          message: `同步完成！成功处理 ${savedItems.length} 个商品${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
          data: {
            user_info: result.data.user_info,
            total_items: items.length,
            saved_items: savedItems,
            errors: errors,
            summary: {
              created: savedItems.filter(item => item.action === 'created').length,
              updated: savedItems.filter(item => item.action === 'updated').length,
              failed: errors.length
            }
          }
        });

      } catch (error) {
        console.error('处理同步结果时出错:', error);
        res.status(500).json({
          success: false,
          message: `处理同步结果时出错: ${error.message}`
        });
      }
    });

    pythonProcess.on('error', (error) => {
      console.error('Python进程启动失败:', error);
      res.status(500).json({
        success: false,
        message: `Python进程启动失败: ${error.message}`
      });
    });

  } catch (error) {
    console.error('同步闲鱼商品错误:', error);
    res.status(500).json({
      success: false,
      message: `同步失败: ${error.message}`
    });
  }
};

/**
 * 获取已同步的闲鱼商品列表
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const getXianyuItems = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword } = req.query;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const where = {
      file_type: 'xianyu'
    };

    if (keyword) {
      where.file_name = { [Op.like]: `%${keyword}%` };
    }

    // 获取闲鱼商品列表
    const { count, rows } = await File.findAndCountAll({
      where,
      order: [['upload_time', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理结果
    const processedItems = rows.map(file => {
      const fileObj = file.toJSON();
      return {
        ...fileObj,
        preview_images_parsed: fileObj.preview_images ? JSON.parse(fileObj.preview_images) : []
      };
    });

    res.json({
      success: true,
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: processedItems
      }
    });

  } catch (error) {
    console.error('获取闲鱼商品列表错误:', error);
    res.status(500).json({
      success: false,
      message: `获取失败: ${error.message}`
    });
  }
};

/**
 * 删除闲鱼商品
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
const deleteXianyuItem = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await File.findOne({
      where: {
        id,
        file_type: 'xianyu'
      }
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: '闲鱼商品不存在'
      });
    }

    await file.destroy();

    res.json({
      success: true,
      message: '删除成功'
    });

  } catch (error) {
    console.error('删除闲鱼商品错误:', error);
    res.status(500).json({
      success: false,
      message: `删除失败: ${error.message}`
    });
  }
};

module.exports = {
  syncXianyuItems,
  getXianyuItems,
  deleteXianyuItem
};
