/*
 Navicat Premium Data Transfer

 Source Server         : Zi<PERSON>huTiHuo_System
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : z<PERSON><PERSON><PERSON><PERSON><PERSON>itong

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 03/08/2025 02:46:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admins
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码(加密)',
  `last_login` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admins
-- ----------------------------
INSERT INTO `admins` VALUES (3, 'admin', '$2b$10$LKBfOM8v5b7FHKDZWJ1dau4fX2p95.8COo0FpTS4njLfPITg6icYu', '2025-08-02 18:11:40', 1, '2025-07-04 18:09:26');

-- ----------------------------
-- Table structure for card_keys
-- ----------------------------
DROP TABLE IF EXISTS `card_keys`;
CREATE TABLE `card_keys`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `key_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡密码',
  `is_used` tinyint(1) NULL DEFAULT 0 COMMENT '是否已使用',
  `used_time` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:有效,0:无效)',
  `used_file_id` int NULL DEFAULT NULL COMMENT '已提取的文件ID',
  `key_type` enum('single','multi') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'single' COMMENT '卡密类型(single:单设备,multi:多设备)',
  `used_devices` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '已使用设备列表,JSON格式存储',
  `max_devices` int NULL DEFAULT 1 COMMENT '最大可用设备数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `key_code`(`key_code` ASC) USING BTREE,
  INDEX `used_file_id`(`used_file_id` ASC) USING BTREE,
  CONSTRAINT `card_keys_ibfk_1` FOREIGN KEY (`used_file_id`) REFERENCES `files` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 307 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of card_keys
-- ----------------------------
INSERT INTO `card_keys` VALUES (284, '4121266CA78C6E5F', 0, '2025-07-07 19:59:42', '2025-07-07 19:54:54', NULL, 1, 17, 'multi', '[{\"device\":\"75dc2277e201a97112570484b9219ecf\",\"fileId\":\"17\"},{\"device\":\"26153be30f1bbca537d3eddec287652b\",\"fileId\":\"17\"},{\"device\":\"5fcecad216f14655ff66b387ec6cb0eb\",\"fileId\":\"17\"}]', 10);
INSERT INTO `card_keys` VALUES (300, '0BD6D1C3CF495B26', 0, NULL, '2025-07-11 17:37:15', NULL, 1, NULL, 'single', NULL, 1);
INSERT INTO `card_keys` VALUES (301, '21341D95A183E458', 0, NULL, '2025-07-11 17:37:15', NULL, 1, NULL, 'single', NULL, 1);
INSERT INTO `card_keys` VALUES (310, '16DF1CD64BBD4804', 0, NULL, '2025-08-02 18:11:52', NULL, 1, NULL, 'single', NULL, 1);

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of categories
-- ----------------------------
INSERT INTO `categories` VALUES (2, '考研资料', '考研资料', '2025-07-04 18:55:23');
INSERT INTO `categories` VALUES (3, 'Windows必装App', 'Windows必装软件', '2025-07-04 18:55:46');
INSERT INTO `categories` VALUES (4, '四六级资料', '四六级资料', '2025-07-07 20:05:44');
INSERT INTO `categories` VALUES (5, 'Java学习资料', 'Java学习资料', '2025-07-07 20:16:01');

-- ----------------------------
-- Table structure for files
-- ----------------------------
DROP TABLE IF EXISTS `files`;
CREATE TABLE `files`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名称',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件路径',
  `file_size` int NULL DEFAULT NULL COMMENT '文件大小(KB)',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '文件描述',
  `important_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '重要文字信息',
  `preview_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '简介图片路径JSON数组',
  `upload_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `download_count` int NULL DEFAULT 0 COMMENT '下载次数',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:可用,0:不可用)',
  `category_id` int NULL DEFAULT NULL COMMENT '分类ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id` ASC) USING BTREE,
  CONSTRAINT `files_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of files
-- ----------------------------
INSERT INTO `files` VALUES (13, '【Visio 2024专业版 | 安装包+详细安装教程 | 一键激活永久使用 | Windows系统专用】', NULL, NULL, 'text', '◆ 软件核心功能与优势\r\n▶ 专业绘图工具：专注流程图、技术路线图、组织结构图、网络拓扑图等绘制，操作界面简洁，新手易上手，高效替代传统绘图软件。\r\n▶ 丰富图形资源库：内置海量专业图形模板、符号及预设样式，涵盖IT、工程、商业等多领域，支持自由调用与自定义编辑。\r\n▶ 多格式导出支持：可导出PDF、PNG、JPG、SVG、Visio原生格式等，满足文档分享、打印、嵌入PPT等多场景需求。\r\n \r\n◆ 系统兼容性\r\n▶ 仅支持Windows 10/11系统（32位/64位均可），不兼容Win7及其他系统，安装前请确认系统版本。\r\n \r\n◆ 激活与使用说明\r\n▶ 激活方式：提供在线安装激活工具，非传统密钥形式，一键完成安装与激活，永久使用，无需重复操作。\r\n\r\n◆ 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 11:14:49', 0, 1, 3);
INSERT INTO `files` VALUES (15, '【Adobe全家桶2025新版Win/Mac全版本合集】', NULL, NULL, 'text', '◆ 可按需挑选2025/2024/2023等年份版本，支持Windows/MacOS系统，含Photoshop/Lightroom/Pr/Ae等全套设计剪辑工具！\r\n \r\n◆ 版本与系统兼容性\r\n➤ 2025最新版特性\r\n➢ 原生适配Windows 10/11（64位）及MacOS \r\n➢原生支持M1/M2/M3/M4/lntel全系列最新苹果芯片\r\n➢ Mac版特别优化：支持Apple Silicon芯片原生运行，告别Rosetta转译卡顿\r\n➤ 提供2017-2025全年份版本\r\n \r\n◆ 软件套装内容\r\n➤ Windows全家桶（20+款）\r\nPS（Photoshop）/PR（Premiere Pro）/AE（After Effects）/AU（Audition）AI（Illustrator）/ID（InDesign）/XD（Experience Design）/LRC（Lightroom Classic）\r\nCH（Character Animator）/AN（Animate）/IC（InCopy）/ME（Media Encoder）BR（Bridge）/DW（Dreamweaver）/PL（Prelude）/DN（Dimension）等\r\n \r\n◆ 安装与使用保障\r\n➤ 便捷安装方案\r\n➢ 每套含独立安装包+图文视频教程，按步骤操作即可激活使用\r\n➢ 无捆绑插件、无二次收费，装完即永久可用（非试用版）\r\n \r\n◆ 发货与售后说明\r\n➤ 交付方式\r\n➢ 夸克网盘、百度网盘、迅雷网盘、不限速网页链接发货\r\n➢ 拍下后系统自动发送链接，24小时内秒发（含周末节假日）\r\n\r\n◆ Mac版LR不能用，只需要安装这个的勿拍\r\n\r\n◆ 重要提示：本电子资料通过夸克网盘、百度网盘、迅雷网盘、不限速网页链接发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 15:43:11', 0, 1, 3);
INSERT INTO `files` VALUES (16, '【JetBrains全家桶一键激活工具】', NULL, NULL, 'text', '◆ 支持IntelliJ IDEA、PyCharm、WebStorm、GoLand等全系开发工具，一键激活至2099年12月31日，覆盖Windows/MacOS全平台\n \n◆ 核心功能与兼容性 \n➤ 全系列产品覆盖 \n➢ 支持IntelliJ IDEA、PyCharm、WebStorm、GoLand、CLion、DataGrip等20+款开发工具专业版\n➢ 兼容2018至2025全系列版本 \n➤ 跨平台适配能力 \n➢ 原生支持Windows 10/11（64位）、macOS （Intel/M1/M2芯片）\n➢ 自动适配系统架构，M1/M2芯片Mac用户无需额外配置\n \n◆ 激活技术特性 \n➤ 一键式激活体验 \n➢ 提供独立可执行程序，无需手动修改配置文件或hosts\n➢ 激活过程自动完成，Windows用户执行脚本后自动添加环境变量 \n➤ 多模型兼容方案 \n➢ 支持JetBrains官方授权协议，激活状态可通过Help > Register验证\n➢ 内置防检测机制，避免因官方反制导致激活失效\n \n◆ 激活流程\n➢ 付款后立即获取云雀链接，内含激活工具、详细图文教程文档\n➢ 需提前在JetBrains官网下载对应开发工具安装包，按教程指引完成激活操作\n➢ 流程简单易懂，零基础用户也能快速上手\n \n◆ 重要提示：本电子资料通过云雀链接发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求。\n \n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱。资料只限于用于学习用途，不得用作任何商业用途。如有资料侵权，请联系我，将立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 16:54:23', 0, 1, 3);
INSERT INTO `files` VALUES (17, '【Navicat 16数据库管理工具（Windows）】', NULL, NULL, 'text', '◆ 跨平台兼容性与数据库支持\r\n➤ 系统适配范围\r\n➢ 原生支持Windows 7/10/11（64位）\r\n➤ 全类型数据库连接\r\n➢ 支持MySQL、PostgreSQL、MariaDB、SQL Server、Oracle、SQLite等关系型数据库\r\n➢ 支持MongoDB、Redis、Snowflake等非关系型数据库连接管理\r\n \r\n◆ 专业数据管理功能\r\n➤ 多维度数据查看编辑\r\n➢ 网格视图/树视图/JSON视图三模式切换，内置编辑器直接操作数据记录\r\n➢ 集成基础数据分析工具，支持可视化图表交互探索数据关系\r\n \r\n◆ 数据迁移与模型设计\r\n➤ 高效迁移解决方案\r\n➢ 数据传输/结构同步/数据同步功能覆盖主流数据库系统\r\n➢ 提供分步向导与差异对比工具，生成可视化变更脚本\r\n➤ 可视化模型构建\r\n➢ 支持关系型数据建模，图形化展示数据库结构与表间关系\r\n➢ 模型与数据库实时同步，确保设计与实际架构一致性\r\n \r\n◆ 企业级功能组件\r\n➤ 安全与性能保障\r\n➢ 安全连接：SSH隧道、SSL加密、多因子身份验证（MFA）保障数据传输\r\n➢ 服务器监控面板：实时性能数据可视化，支持MongoDB模式分析\r\n➤ 自动化与备份管理\r\n➢ 数据生成器：按业务规则生成带参照完整性的测试数据集\r\n➢ 备份还原：支持MongoDump、Oracle Data Pump等工具GUI化操作，计划任务自动执行\r\n \r\n◆ 跨平台使用体验\r\n➤ 系统原生设计\r\n➢ Windows原生应用架构，确保系统资源高效利用\r\n➢ 深色模式适配，降低长时间使用视觉疲劳\r\n➤ 数据交互能力\r\n➢ 支持Excel/Access/CSV等10+格式批量导入导出\r\n➢ 数据字典功能生成可定制化PDF文档，支持团队协作分享\r\n\r\n◆ 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 16:56:43', 0, 1, 3);
INSERT INTO `files` VALUES (22, '【Navicat 17数据库管理工具（Windows/Mac双平台）】', NULL, NULL, 'text', '◆ 全场景数据管理能力\r\n➤ 多维度数据查看与编辑\r\n➢ 支持网格视图、树视图、JSON视图无缝切换，内置编辑器直接操作数据记录\r\n➢ 集成数据分析工具与可视化图表交互功能，实时探索数据趋势与模式\r\n➤ 专业级对象管理与查询优化\r\n➢ 对象设计器：结构化选项卡分解复杂查询编写流程，提升SQL编写准确性\r\n➢ 查询编辑调试器：代码补全、代码片段加速编程，可视化执行计划分析查询效率\r\n➢ 支持设置断点、变量监控、调用堆栈检查，快速定位编码错误\r\n \r\n◆ 智能与协作功能升级\r\n➤ AI辅助数据库操作\r\n➢ AI助手支持ChatGPT、Deepseek、Google Gemini、Ollama等模型，提问即得查询优化建议\r\n➢ 结合数据库上下文生成精准SQL语句，降低技术门槛\r\n➤ 团队协同与云同步\r\n➢ 连接配置、查询脚本、模型工作区、BI仪表板全量同步至云服务\r\n➢ 支持多成员实时共享协作，跨设备访问历史工作记录\r\n \r\n◆ 高效迁移与模型设计\r\n➤ 数据迁移解决方案\r\n➢ 数据传输、结构同步、数据同步功能覆盖主流数据库系统\r\n➢ 提供分步操作向导，支持批量迁移与差异对比，生成可视化变更脚本\r\n➤ 可视化模型构建\r\n➢ 支持关系型、维度、数据仓库2.0等多种建模方法，图形化展示数据库结构\r\n➢ 实时同步模型与实际数据库，确保设计完整性与系统可维护性\r\n \r\n◆ 企业级功能组件\r\n➤ 安全与性能保障\r\n➢ 安全连接：SSH隧道、SSL/TLS加密、PAM/LDAP/Kerberos等多重身份验证\r\n➢ 服务器监控：实时性能分析、资源占用可视化，支持MongoDB模式分析器定位异常值\r\n➤ 自动化与备份\r\n➢ 数据生成器：按业务规则生成带参照完整性的测试数据集，满足开发场景需求\r\n➢ 备份还原：支持MongoDump、Oracle Data Pump等工具GUI化操作，计划任务自动运行并邮件通知\r\n \r\n◆ 跨平台适配与用户体验\r\n➤ 系统兼容性\r\n➢ 原生支持Windows 10/11（64位）、macOS 10.13+（Intel/M1/M2芯片）\r\n➤ 细节优化设计\r\n➢ 深色模式保护视力，原生应用架构确保系统流畅运行\r\n➢ 支持Excel/Access/CSV等20+格式导入导出，数据字典生成可定制化PDF文档\r\n\r\n◆ 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:03:26', 0, 1, 3);
INSERT INTO `files` VALUES (23, '【2026考研数学笔记「王炸包」｜4位名师笔记】', NULL, NULL, 'text', '[火] 核心王炸： \r\n- 名师天团：张宇/汤家凤/武忠祥/李永乐笔记（解题逻辑+易错标红，直接抄思路）\r\n- 阶段通杀：公式手册（基础速记）→ 真题题型分类（强化突破）→ 22-24模拟卷+25冲刺模板（锁分必用）\r\n- 题库提纯：880/1000/1800筛出必刷核心题，刷题省50%时间\r\n \r\n[钉子] 适配人群： \r\n基础弱想“抄底”｜强化卡壳求破局｜冲刺缺真题/模板｜时间紧直接“开挂”\r\n \r\n[钉子] 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n[钉子] 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:04:29', 0, 1, 2);
INSERT INTO `files` VALUES (24, '【英语六级真题电子版 | 全年份备考合集】', NULL, NULL, 'text', '2024年12月3套最新真题已更新含听力音频（MP3）+解析+原文，紧贴考纲新题型，考前必刷！\r\n全年份覆盖：1990-2024年12月真题\r\n收录改革前后完整试卷，从基础刷题到冲刺拔高一站式备齐，适配各阶段考生。\r\n■ 资料包核心内容（高清无水印可打印） \r\n≫ 历年真题卷（附标准答案）+逐题解析（听力/阅读/翻译/写作全模块拆解）\r\n≫ 听力音频（原速+倍速双版本，解析内标注出题时间轴）\r\n≫ 高频词汇表（按词频分类，配真题例句）\r\n≫ 考试答题卡模板+一键打印版格式\r\n■ 下单即享三大优势\r\n≫ 标价1元全款，秒拍秒发（百度网盘/夸克双渠道直发）\r\n≫ 24小时自动发货，无需等待，立即获取电子资料\r\n≫ 配套资源完整，从真题训练到答题技巧全流程辅助 \r\n\r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n[钉子] 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:05:26', 0, 1, 4);
INSERT INTO `files` VALUES (25, '【2026考研英语「全明星急救包」｜高分资料合集】', NULL, NULL, 'text', ' 五大模块高效提分攻略：\r\n■ 词汇攻坚体系：\r\n核心词汇笔记+真题高频词手册+易错词分类表，搭配28天速记方案，单词记忆效率翻倍！ \r\n■ 长难句突破方案：\r\n语法精华笔记+长难句66句拆解+阅读核心句型精讲，提炼结构拆分公式，复杂句型秒懂！ \r\n■ 阅读提分组合：\r\n阅读逻辑精华+双解题技巧手册+真题逐词翻译，从“读不懂”到“抓考点”，正确率直线提升！ \r\n■ 作文急救套装：\r\n高分作文模板库+作文练字帖，图表/议论文/书信话题全覆盖，考场直接套用！ \r\n■ 真题全解方案：\r\n完型/新题型/翻译解题套路+真题深度解析，分题型拆解命题逻辑，吃透真题=锁定考点！\r\n \r\n◎ 资料核心亮点：\r\n考研英语全模块精华资料汇总，覆盖语法、阅读、写作等解题技巧，高分备考经验一站式整合！ \r\n◆ 适合人群：\r\n✔ 英语基础薄弱想短期提分\r\n✔ 阅读错题率高需技巧突破\r\n✔ 作文无模板、单词记忆困难\r\n✔ 真题刷不透的考研党\r\n \r\n[钉子] 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n[钉子] 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:06:59', 0, 1, 2);
INSERT INTO `files` VALUES (26, 'Your Uninstaller!（Windows版）', NULL, NULL, 'text', '一、软件基本信息 \r\n1. 名称：Your Uninstaller! 专业卸载工具（Windows版本）\r\n2. 系统支持：Windows 7/8/10/11（32/64位系统） \r\n二、核心功能亮点 \r\n1. 深度彻底卸载\r\n- 比系统自带卸载更彻底，扫描并清除软件残留注册表、缓存文件及关联数据。\r\n- 支持强制卸载损坏或残留的程序，解决卸载失败问题。\r\n2. 智能程序管理\r\n- 可视化软件列表，按使用频率、占用空间排序，快速定位冗余程序。\r\n- 实时监控新安装软件的变更记录，支持卸载回溯。\r\n3. 系统优化辅助\r\n- 启动项管理：禁用不必要的开机程序，提升系统启动速度。\r\n- 浏览器插件清理：批量卸载浏览器工具栏、扩展程序，修复主页劫持。\r\n4. 批量高效操作\r\n- 支持多选程序批量卸载，节省逐个操作时间。\r\n- 软件更新检测：提示已安装程序的新版本，避免使用过时软件。 \r\n三、使用提示 \r\n1. 建议首次使用时先扫描系统，生成软件清单后再选择性卸载。\r\n2. 对不确定的程序可先查看“安装日志”，确认功能后再操作。 \r\n四、交易与申明 \r\n1. 服务说明 \r\n- 费用包含软件文件整理、远程安装指导（非软件版权费用），仅限个人学习研究使用，严禁商用。\r\n- 提供安装包及基础使用教程，支持Windows系统环境配置咨询。 \r\n2. 售后规则 \r\n- 数字商品具有复制性，一经发货不支持退换，下单前请确认系统兼容性。\r\n- 若遇侵权投诉，将以《计算机软件保护条例》第十七条（学习研究用途）作为申诉依据。\r\n\r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:08:14', 1, 1, 3);
INSERT INTO `files` VALUES (27, 'Typora（Windows 1.9.5版本）', NULL, NULL, 'text', '一、软件基本信息 \r\n1. 名称：Typora Markdown编辑器（Windows 1.9.5版）\r\n2. 系统支持：Windows 7/8/10/11（64位系统） \r\n二、核心功能亮点 \r\n1. 所见即所得编辑\r\n- 实时渲染Markdown格式（标题/列表/表格/代码块等），无需手动切换预览模式。\r\n- 支持数学公式（LaTeX）、图片拖拽插入、自动保存。\r\n2. 多场景高效写作\r\n- 内置大纲导航与文件管理面板，方便文档结构整理。\r\n- 一键导出PDF/Word/HTML/EPUB等格式，适配写作、文档、博客等场景。\r\n3. 个性化与兼容性\r\n- 6种默认主题（含暗黑模式），支持自定义CSS样式。\r\n- 深度优化Windows系统，启动快速，兼容最新Windows 11。 \r\n三、使用提示 \r\n1. 适合新手入门Markdown，内置基础教程可快速上手。\r\n2. 推荐搭配图床工具（如PicGo）实现图片云端存储。 \r\n四、申明与注意事项 \r\n1. 交易与售后说明 \r\n- 软件一经发货，因具有数字复制性，不支持任何理由退换，介意勿拍；若产生客服介入，本申明将作为处理依据。 \r\n2. 费用与用途说明 \r\n- 收取费用仅为软件文件整理、存储及远程安装指导服务费用（非软件版权费用）。\r\n- 服务仅限个人学习研究使用，专为新手提供安装与基础功能指导，严禁用于商业用途或二次分发。\r\n- 建议通过官方渠道购买正版授权，以获取完整服务与技术支持。 \r\n3. 法律依据说明 \r\n根据《计算机软件保护条例》（2013年修订版）第十七条规定：“为了学习和研究软件内含的设计思想和原理，通过安装、显示、传输或者存储软件等方式使用软件的，可以不经软件著作权人许可，不向其支付报酬。” 本服务仅支持符合上述条款的学习研究用途。 \r\n\r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:09:03', 1, 1, 3);
INSERT INTO `files` VALUES (28, '【2026考研必入】数学1000数二做题本PDF！高清无水印可打印，原书排版超清晰', NULL, NULL, 'text', '[钉子]核心优势速览：\r\n[右] 2026最新适配：紧扣考研大纲，函数极限/导数微分/积分等全章节覆盖，复习进度不落后！\r\n[右] 高清PDF无水印：原书扫描+优化处理，打印字迹锐利，平板缩放无模糊，适配多种设备！\r\n[右] 完整目录+原书答案：章节索引秒定位，答案解析详细到步骤，刷题纠错超省心\r\n[右] 多场景实用：打印装订当纸质题册，平板分屏边看边写，通勤学习无缝衔接\r\n \r\n[钉子]适合人群：\r\n2026考研数二考生 | 正在刷1000题巩固基础 | 需要系统刷题资料的同学\r\n[钉子]点击“我想要”立即获取，备考黄金期别错过，早刷早提分！\r\n \r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n\r\n[钉子]免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:10:40', 0, 1, 2);
INSERT INTO `files` VALUES (29, '【2026考研必入】数学880数二做题本PDF！高清无水印可打印，原书排版超清晰', NULL, NULL, 'text', '[钉子]核心优势速览：\r\n[右] 2026最新适配：紧扣考研大纲，函数极限/导数微分/积分等全章节覆盖，复习进度不落后！\r\n[右] 高清PDF无水印：原书扫描+优化处理，打印字迹锐利，平板缩放无模糊，适配多种设备！\r\n[右] 完整目录+原书答案：章节索引秒定位，答案解析详细到步骤，刷题纠错超省心\r\n[右] 多场景实用：打印装订当纸质题册，平板分屏边看边写，通勤学习无缝衔接\r\n \r\n[钉子]适合人群：\r\n2026考研数二考生 | 正在刷880题巩固基础 | 需要系统刷题资料的同学\r\n[钉子]点击“我想要”立即获取，备考黄金期别错过，早刷早提分！\r\n \r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n\r\n[钉子]免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:11:33', 2, 1, 2);
INSERT INTO `files` VALUES (30, '【2026张宇考研数学全程班｜官网原版高清无水印｜秒发速更｜售后无忧】', NULL, NULL, 'text', '◆【课程全覆盖】\r\n➤ 课程科目：数学一/数学二/数学三\r\n➤ 基础阶段：基础30讲+配套习题课\r\n➤ 强化阶段：高数18讲+线代9讲+概率9讲+1000题+1000题解析\r\n➤ 冲刺阶段：真题大串讲+命题人8套卷/4套卷解析+押题班\r\n➤ 额外赠送：2025考研数学张宇合集\r\n \r\n◆【核心卖点】\r\n➤ 官网原版高清录制，无水印无卡顿，同步机构更新\r\n➤ 全年分阶段更新计划：基础（1-5月）→强化（5-9月）→冲刺（9-12月）→押题（11-12月），每日跟进不延误\r\n➤ 专属售后：全年在线答疑，更新异常秒处理，永不断更保障\r\n\r\n◆ 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:12:29', 0, 1, 2);
INSERT INTO `files` VALUES (31, '【2026考研必入】数学660数二做题本PDF！高清无水印可打印，原书排版超清晰', NULL, NULL, 'text', '[钉子]核心优势速览：\r\n[右] 2026最新适配：紧扣考研大纲，函数极限/导数微分/积分等全章节覆盖，复习进度不落后！\r\n[右] 高清PDF无水印：原书扫描+优化处理，打印字迹锐利，平板缩放无模糊，适配多种设备！\r\n[右] 完整目录+原书答案：章节索引秒定位，答案解析详细到步骤，刷题纠错超省心\r\n[右] 多场景实用：打印装订当纸质题册，平板分屏边看边写，通勤学习无缝衔接\r\n \r\n[钉子]适合人群：\r\n2026考研数二考生 | 正在刷660题巩固基础 | 需要系统刷题资料的同学\r\n[钉子]点击“我想要”立即获取，备考黄金期别错过，早刷早提分！\r\n \r\n[钉子] 重要提示：本电子资料通过百度网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n[钉子]免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:13:26', 5, 1, 2);
INSERT INTO `files` VALUES (32, '【2026考研计算机408笔记「全能资料包」｜王道+真题解析', NULL, NULL, 'text', '[钉子] 为什么选这套资料？ \r\n▶️「全科覆盖+精准分类」，省时间就是提分！\r\n▶️科目全：数据结构、计算机组成原理、操作系统、计算机网络四大核心科目，从基础到冲刺无死角覆盖；\r\n▶️类型全：笔记、课件、做题本、真题解析、冲刺手册…你需要的全都有；\r\n▶️分类细：每个资料单独建文件夹（如「王道做题本」「408强化课件」），命名清晰，打开直接用，不用浪费时间整理！\r\n \r\n[钉子] 资料核心亮点（必看！） \r\n▶️ 「王道+热门题库」刷题体系（提分关键！） \r\n- 王道全家桶：25年王道书籍做题本、408真题做题本、抓码1000题做题本、强化打卡表+完整PPT，同步课程节奏，边学边练；\r\n- 小众优质题：竟成408、摘星题库做题本，搭配王道强化，针对性突破薄弱点； \r\n▶️ 「最新课件+冲刺资料」 \r\n- 强化阶段：【408强化课件】【操作系统/计网/组成原理/数据结构 强化课】带「更新」标记，紧跟25考研最新考点；\r\n- 冲刺阶段：【408冲刺考点】【冲刺背诵手册（书签版）】，浓缩核心知识点，速记提分； \r\n▶️ 「笔记+辅助资料」（效率翻倍！） \r\n- 笔记精华：25年付费笔记（精炼考点）+ 王道408笔记（体系化梳理）+ 往年适用笔记（对比查漏）；\r\n \r\n[钉子] 适合谁？ \r\n▶️26考研计算机408考生（无论基础/强化/冲刺阶段）；\r\n▶️想一套资料解决所有需求，不想浪费时间找资源的同学；\r\n▶️追求「最新考点+体系化复习+高效刷题」的上岸选手！\r\n\r\n[钉子] 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n[钉子] 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！\r\n#考研冲刺 #考研上岸冲刺资料', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:14:12', 1, 1, 2);
INSERT INTO `files` VALUES (33, '【2025最新Java面试题合集 八股文+面试突击文档（14万字全）】', NULL, NULL, 'text', '◆内容涵盖\r\n▶核心技术模块全覆盖：包含Java基础（语法、面向对象、常用类等）、并发编程（线程、锁、线程池等）、JVM（内存模型、垃圾回收、类加载等）、MySQL（索引、事务、优化等）、Redis（数据结构、缓存策略等）、SSM（Spring、SpringMVC、MyBatis核心原理）、微服务（服务治理、分布式协调等）、RabbitMQ（消息队列原理、场景应用）、ES（搜索引擎基础、查询优化）、开发工具（IDEA、Maven等使用技巧）。\r\n▶实战导向内容：收录真实学员高频面试题（含常见问答思路）、黑马项目总结（项目架构、技术难点解决方案），贴合企业面试实战场景。\r\n▶内容体量：总字数达14万字，知识点密集，覆盖从基础到进阶的全维度面试考点。\r\n \r\n◆文档优势\r\n▶时效性强：2025版紧跟最新技术趋势与企业面试重点，持续更新内容，确保信息不过时。\r\n▶实用性高：整合“八股文”核心要点与面试突击技巧，既适合系统梳理知识，也适合短期集中备考。\r\n▶适配不同需求：2025版侧重最新考点，2024版提供可编辑版本，满足不同学习习惯（在线查阅/本地整理）。\r\n \r\n◆发货方式\r\n▶WPS链接（2025年版）： \r\n➢ 不可下载、不可编辑，支持在线查阅；\r\n➢ 内容持续更新，实时同步最新面试题与解析。\r\n▶飞书链接（2024年版）：\r\n➢ 可下载、可编辑，方便本地标注、整理笔记；\r\n➢ 附带Word与PDF文档，适配不同设备查阅需求。\r\n \r\n◆适用人群\r\n▶应届生/校招群体：系统补充Java核心知识，应对基础考点；\r\n▶在职开发者跳槽：突击高阶考点（如并发、微服务），提升面试竞争力；\r\n▶短期备考者：通过密集知识点与高频题，快速提升应试能力。\r\n \r\n◆ 重要提示：本电子资料通过百度/夸克双网盘发送，24小时内秒发且支持永久保存。因网盘资源具有可复制传播特性，链接发出后无法撤回或限制使用，故一经发送后不支持任何形式的退换货，请您下单前确认需求哦~\r\n\r\n◆ 免责声明：本人所有资料的标价均为帮忙收集整理资料的辛苦费，并不是资料的价钱，资料只限于用于学习用途，不得用作任何商业用途。如有哪些资料侵权，联系我，立马下架删除！', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-07 20:15:41', 2, 1, 5);
INSERT INTO `files` VALUES (34, '作文.png', 'images\\1752167675559-1b189a5a-582a-44c1-8c80-ea00dfc056bd.png', 68, 'png', '11111111111111', '1234567891011121314151617181920210103040524141111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111555555555555555555555555555555557899999999999', NULL, '2025-07-10 16:38:38', 2, 1, 2);
INSERT INTO `files` VALUES (41, '1111111111111', NULL, NULL, 'text', '1111111111111111111', '11111111111111', '[\"preview-1754155857513-8a052b1a-c9da-4b49-95f3-e7f24e14515c.jpeg\"]', '2025-08-02 17:30:57', 1, 1, 5);
INSERT INTO `files` VALUES (42, '22222222222222222222222222.jpeg', 'images\\1754157296508-80e70925-884d-4ea1-8bdd-77aef8423192.jpeg', 131, 'jpeg', '222222222222222222222222222222222222222222222', '', '[\"preview-1754157296512-433f44d1-e143-4b01-99e9-97d592fb1407.jpeg\",\"preview-1754159517154-5f6fbee5-f68b-448e-a660-3cab55049ebe.jpeg\"]', '2025-08-02 17:54:56', 0, 1, 5);

-- ----------------------------
-- Table structure for operation_logs
-- ----------------------------
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作描述',
  `operator_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作IP',
  `card_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '相关卡密',
  `file_id` int NULL DEFAULT NULL COMMENT '相关文件ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 795 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of operation_logs
-- ----------------------------
INSERT INTO `operation_logs` VALUES (649, '提取文件', '用户提取文件资料, 设备: 7f3cc25d396b4568285e69e7c34a7f09', '::1', '4751F9448AC13F18', 34, '2025-07-10 16:38:53');
INSERT INTO `operation_logs` VALUES (653, '提取文件', '用户提取文件资料, 设备: 7f3cc25d396b4568285e69e7c34a7f09', '::1', '4E89EAAC751817AC', 33, '2025-07-10 16:39:30');
INSERT INTO `operation_logs` VALUES (662, '提取文件', '用户提取文件资料, 设备: 7f3cc25d396b4568285e69e7c34a7f09', '::1', '202607081824AF6EE78D4086', 32, '2025-07-10 16:47:12');
INSERT INTO `operation_logs` VALUES (666, '提取文件', '用户提取文件资料, 设备: 7f3cc25d396b4568285e69e7c34a7f09', '::1', 'E6CD0F45B816EBDA', 31, '2025-07-10 16:47:39');
INSERT INTO `operation_logs` VALUES (669, '替换文件', '管理员替换文件内容', '::1', NULL, NULL, '2025-07-10 17:14:35');
INSERT INTO `operation_logs` VALUES (670, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-10 17:30:44');
INSERT INTO `operation_logs` VALUES (671, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 11:49:08');
INSERT INTO `operation_logs` VALUES (672, '生成卡密', '管理员生成卡密', '::1', NULL, NULL, '2025-07-11 11:49:47');
INSERT INTO `operation_logs` VALUES (673, '卡密验证', '用户验证卡密', '::1', '18BB7C84694729F2', NULL, '2025-07-11 11:49:54');
INSERT INTO `operation_logs` VALUES (674, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 11:54:17');
INSERT INTO `operation_logs` VALUES (675, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:08:39');
INSERT INTO `operation_logs` VALUES (676, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:10:47');
INSERT INTO `operation_logs` VALUES (677, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:10:47');
INSERT INTO `operation_logs` VALUES (678, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:11:53');
INSERT INTO `operation_logs` VALUES (679, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:11:53');
INSERT INTO `operation_logs` VALUES (680, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:11:53');
INSERT INTO `operation_logs` VALUES (681, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:11:54');
INSERT INTO `operation_logs` VALUES (682, '设置闲鱼令牌', '管理员设置闲鱼API访问令牌', '::1', NULL, NULL, '2025-07-11 12:13:55');
INSERT INTO `operation_logs` VALUES (683, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:14:01');
INSERT INTO `operation_logs` VALUES (684, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:14:02');
INSERT INTO `operation_logs` VALUES (685, '设置闲鱼令牌', '管理员设置闲鱼API访问令牌', '::1', NULL, NULL, '2025-07-11 12:15:33');
INSERT INTO `operation_logs` VALUES (686, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:15:37');
INSERT INTO `operation_logs` VALUES (687, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:15:38');
INSERT INTO `operation_logs` VALUES (688, '设置闲鱼令牌', '管理员设置闲鱼API访问令牌', '::1', NULL, NULL, '2025-07-11 12:15:49');
INSERT INTO `operation_logs` VALUES (689, '设置闲鱼令牌', '管理员设置闲鱼API访问令牌', '::1', NULL, NULL, '2025-07-11 12:16:00');
INSERT INTO `operation_logs` VALUES (690, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:19:49');
INSERT INTO `operation_logs` VALUES (691, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:09');
INSERT INTO `operation_logs` VALUES (692, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:09');
INSERT INTO `operation_logs` VALUES (693, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:09');
INSERT INTO `operation_logs` VALUES (694, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:55');
INSERT INTO `operation_logs` VALUES (695, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:55');
INSERT INTO `operation_logs` VALUES (696, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:56');
INSERT INTO `operation_logs` VALUES (697, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:56');
INSERT INTO `operation_logs` VALUES (698, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:56');
INSERT INTO `operation_logs` VALUES (699, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:22:57');
INSERT INTO `operation_logs` VALUES (700, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 12:25:09');
INSERT INTO `operation_logs` VALUES (701, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:25:26');
INSERT INTO `operation_logs` VALUES (702, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:25:26');
INSERT INTO `operation_logs` VALUES (703, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:50');
INSERT INTO `operation_logs` VALUES (704, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:50');
INSERT INTO `operation_logs` VALUES (705, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:51');
INSERT INTO `operation_logs` VALUES (706, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:51');
INSERT INTO `operation_logs` VALUES (707, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:51');
INSERT INTO `operation_logs` VALUES (708, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:51');
INSERT INTO `operation_logs` VALUES (709, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:52');
INSERT INTO `operation_logs` VALUES (710, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 12:27:52');
INSERT INTO `operation_logs` VALUES (711, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 12:27:58');
INSERT INTO `operation_logs` VALUES (712, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 15:46:46');
INSERT INTO `operation_logs` VALUES (713, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 16:11:20');
INSERT INTO `operation_logs` VALUES (714, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 16:11:52');
INSERT INTO `operation_logs` VALUES (715, '卡密验证', '用户验证卡密', '::1', '18BB7C84694729F2', NULL, '2025-07-11 16:30:19');
INSERT INTO `operation_logs` VALUES (717, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:30:36');
INSERT INTO `operation_logs` VALUES (718, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:30:36');
INSERT INTO `operation_logs` VALUES (719, '卡密验证', '用户验证卡密', '::1', '6B4FD3F84304ADE0', NULL, '2025-07-11 16:44:14');
INSERT INTO `operation_logs` VALUES (721, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:44:22');
INSERT INTO `operation_logs` VALUES (722, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:44:22');
INSERT INTO `operation_logs` VALUES (723, '卡密验证', '用户验证卡密', '::1', 'CDE07722C2A5374E', NULL, '2025-07-11 16:50:22');
INSERT INTO `operation_logs` VALUES (725, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:50:29');
INSERT INTO `operation_logs` VALUES (726, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 16:50:29');
INSERT INTO `operation_logs` VALUES (727, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 16:52:04');
INSERT INTO `operation_logs` VALUES (728, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-11 16:55:57');
INSERT INTO `operation_logs` VALUES (729, '卡密验证', '用户验证卡密', '::1', 'CAA8300409478D7A', NULL, '2025-07-11 16:58:07');
INSERT INTO `operation_logs` VALUES (730, '提取文件', '用户提取文件资料', '::1', 'CAA8300409478D7A', 29, '2025-07-11 16:58:11');
INSERT INTO `operation_logs` VALUES (731, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:00:15');
INSERT INTO `operation_logs` VALUES (732, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:00:15');
INSERT INTO `operation_logs` VALUES (733, '批量删除提取记录', '管理员批量删除提取记录', '::1', NULL, NULL, '2025-07-11 17:35:58');
INSERT INTO `operation_logs` VALUES (734, '批量删除提取记录', '管理员批量删除提取记录', '::1', NULL, NULL, '2025-07-11 17:36:04');
INSERT INTO `operation_logs` VALUES (735, '批量删除提取记录', '管理员批量删除提取记录', '::1', NULL, NULL, '2025-07-11 17:36:11');
INSERT INTO `operation_logs` VALUES (736, '卡密验证', '用户验证卡密', '::1', '92EE0D374D94DC06', NULL, '2025-07-11 17:37:21');
INSERT INTO `operation_logs` VALUES (737, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:37:22');
INSERT INTO `operation_logs` VALUES (738, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:37:22');
INSERT INTO `operation_logs` VALUES (739, '提取文件', '用户提取文件资料', '::1', '92EE0D374D94DC06', 27, '2025-07-11 17:37:26');
INSERT INTO `operation_logs` VALUES (740, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:37:31');
INSERT INTO `operation_logs` VALUES (741, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-11 17:37:31');
INSERT INTO `operation_logs` VALUES (742, '卡密验证', '用户验证卡密', '::1', 'A346D609BAD6D2DB', NULL, '2025-07-12 14:32:00');
INSERT INTO `operation_logs` VALUES (743, '提取文件', '用户提取文件资料', '::1', 'A346D609BAD6D2DB', 26, '2025-07-12 14:32:05');
INSERT INTO `operation_logs` VALUES (744, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-12 14:32:13');
INSERT INTO `operation_logs` VALUES (745, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-12 14:32:13');
INSERT INTO `operation_logs` VALUES (746, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-17 15:36:35');
INSERT INTO `operation_logs` VALUES (747, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-18 01:37:14');
INSERT INTO `operation_logs` VALUES (748, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-18 01:58:37');
INSERT INTO `operation_logs` VALUES (749, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-18 02:01:33');
INSERT INTO `operation_logs` VALUES (750, '卡密验证', '用户验证卡密', '::1', '26D3074F9D25EF3B', NULL, '2025-07-18 10:45:12');
INSERT INTO `operation_logs` VALUES (751, '提取文件', '用户提取文件资料', '::1', '26D3074F9D25EF3B', 33, '2025-07-18 10:45:29');
INSERT INTO `operation_logs` VALUES (752, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 10:45:33');
INSERT INTO `operation_logs` VALUES (753, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 10:45:34');
INSERT INTO `operation_logs` VALUES (754, '卡密验证', '用户验证卡密', '::1', '1E8E13170963C182', NULL, '2025-07-18 10:52:25');
INSERT INTO `operation_logs` VALUES (755, '提取文件', '用户提取文件资料', '::1', '1E8E13170963C182', 29, '2025-07-18 10:52:55');
INSERT INTO `operation_logs` VALUES (756, '系统清理', '自动清理了4个过期和使用过的卡密 (过期: 2, 已使用: 2)', '127.0.0.1', NULL, NULL, '2025-07-18 11:20:59');
INSERT INTO `operation_logs` VALUES (757, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 11:27:52');
INSERT INTO `operation_logs` VALUES (758, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 11:27:52');
INSERT INTO `operation_logs` VALUES (759, '控制定时任务', '管理员控制定时清理任务', '::1', NULL, NULL, '2025-07-18 11:37:10');
INSERT INTO `operation_logs` VALUES (760, '控制定时任务', '管理员控制定时清理任务', '::1', NULL, NULL, '2025-07-18 11:37:19');
INSERT INTO `operation_logs` VALUES (761, '手动清理卡密', '管理员手动清理过期和使用过的卡密', '::1', NULL, NULL, '2025-07-18 11:37:30');
INSERT INTO `operation_logs` VALUES (762, '手动完整清理', '管理员手动执行完整系统清理', '::1', NULL, NULL, '2025-07-18 11:37:56');
INSERT INTO `operation_logs` VALUES (763, '清理过期卡密', '管理员清理过期卡密', '::1', NULL, NULL, '2025-07-18 15:28:14');
INSERT INTO `operation_logs` VALUES (764, '清理已使用卡密', '管理员清理已使用卡密', '::1', NULL, NULL, '2025-07-18 15:59:33');
INSERT INTO `operation_logs` VALUES (765, '卡密验证', '用户验证卡密', '::1', 'RECENT_97C0A81B', NULL, '2025-07-18 16:03:38');
INSERT INTO `operation_logs` VALUES (766, '提取文件', '用户提取文件资料', '::1', 'RECENT_97C0A81B', 31, '2025-07-18 16:03:44');
INSERT INTO `operation_logs` VALUES (767, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 16:03:52');
INSERT INTO `operation_logs` VALUES (768, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-18 16:03:52');
INSERT INTO `operation_logs` VALUES (769, '清理无效卡密', '管理员清理无效卡密', '::1', NULL, NULL, '2025-07-18 16:04:19');
INSERT INTO `operation_logs` VALUES (770, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-19 16:16:13');
INSERT INTO `operation_logs` VALUES (771, '卡密验证', '用户验证卡密', '::1', '7F40F0F82C704C67', NULL, '2025-07-19 16:49:03');
INSERT INTO `operation_logs` VALUES (772, '提取文件', '用户提取文件资料', '::1', '7F40F0F82C704C67', 31, '2025-07-19 16:49:20');
INSERT INTO `operation_logs` VALUES (773, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-19 16:49:36');
INSERT INTO `operation_logs` VALUES (774, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-19 16:49:36');
INSERT INTO `operation_logs` VALUES (775, '清理无效卡密', '管理员清理无效卡密', '::1', NULL, NULL, '2025-07-19 16:50:50');
INSERT INTO `operation_logs` VALUES (776, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-21 17:57:29');
INSERT INTO `operation_logs` VALUES (777, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-22 11:37:51');
INSERT INTO `operation_logs` VALUES (778, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-25 16:06:51');
INSERT INTO `operation_logs` VALUES (779, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 04:03:19');
INSERT INTO `operation_logs` VALUES (780, '修改密码', '管理员修改密码', '::1', NULL, NULL, '2025-07-26 04:07:59');
INSERT INTO `operation_logs` VALUES (781, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 04:08:09');
INSERT INTO `operation_logs` VALUES (782, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 04:09:47');
INSERT INTO `operation_logs` VALUES (783, '修改密码', '管理员修改密码', '::1', NULL, NULL, '2025-07-26 04:10:03');
INSERT INTO `operation_logs` VALUES (784, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 04:10:10');
INSERT INTO `operation_logs` VALUES (785, '卡密验证', '用户验证卡密', '::1', '1A94188429519E5B', NULL, '2025-07-26 04:21:54');
INSERT INTO `operation_logs` VALUES (786, '提取文件', '用户提取文件资料', '::1', '1A94188429519E5B', 31, '2025-07-26 04:22:02');
INSERT INTO `operation_logs` VALUES (787, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-26 04:22:28');
INSERT INTO `operation_logs` VALUES (788, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-07-26 04:22:28');
INSERT INTO `operation_logs` VALUES (789, '清理无效卡密', '管理员清理无效卡密', '::1', NULL, NULL, '2025-07-26 17:00:49');
INSERT INTO `operation_logs` VALUES (790, '卡密验证', '用户验证卡密', '::1', '5157BDB34CD8275B', NULL, '2025-07-26 17:01:33');
INSERT INTO `operation_logs` VALUES (791, '提取文件', '用户提取文件资料', '::1', '5157BDB34CD8275B', 31, '2025-07-26 17:01:40');
INSERT INTO `operation_logs` VALUES (792, '卡密验证', '用户验证卡密', '::1', 'DADF406279AA55F2', NULL, '2025-07-26 17:25:24');
INSERT INTO `operation_logs` VALUES (793, '提取文件', '用户提取文件资料', '::1', 'DADF406279AA55F2', 34, '2025-07-26 17:25:30');
INSERT INTO `operation_logs` VALUES (794, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 17:45:15');
INSERT INTO `operation_logs` VALUES (795, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-07-26 17:46:10');
INSERT INTO `operation_logs` VALUES (796, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-08-02 16:37:54');
INSERT INTO `operation_logs` VALUES (797, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-08-02 17:01:16');
INSERT INTO `operation_logs` VALUES (798, '卡密验证', '用户验证卡密', '::1', '96B8DAC1D819AAAA', NULL, '2025-08-02 17:01:31');
INSERT INTO `operation_logs` VALUES (799, '提取文件', '用户提取文件资料', '::1', '96B8DAC1D819AAAA', 35, '2025-08-02 17:01:55');
INSERT INTO `operation_logs` VALUES (800, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:06:50');
INSERT INTO `operation_logs` VALUES (801, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:06:50');
INSERT INTO `operation_logs` VALUES (802, '清理无效卡密', '管理员清理无效卡密', '::1', NULL, NULL, '2025-08-02 17:07:39');
INSERT INTO `operation_logs` VALUES (803, '卡密验证', '用户验证卡密', '::1', '1841DB6F0EA2AB06', NULL, '2025-08-02 17:07:43');
INSERT INTO `operation_logs` VALUES (804, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:08:00');
INSERT INTO `operation_logs` VALUES (805, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:08:00');
INSERT INTO `operation_logs` VALUES (806, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:16');
INSERT INTO `operation_logs` VALUES (807, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:16');
INSERT INTO `operation_logs` VALUES (808, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:39');
INSERT INTO `operation_logs` VALUES (809, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:39');
INSERT INTO `operation_logs` VALUES (810, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:43');
INSERT INTO `operation_logs` VALUES (811, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:12:44');
INSERT INTO `operation_logs` VALUES (812, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:14:30');
INSERT INTO `operation_logs` VALUES (813, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:14:30');
INSERT INTO `operation_logs` VALUES (814, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:14:35');
INSERT INTO `operation_logs` VALUES (815, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:14:35');
INSERT INTO `operation_logs` VALUES (816, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:15:42');
INSERT INTO `operation_logs` VALUES (817, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:15:42');
INSERT INTO `operation_logs` VALUES (818, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:19:49');
INSERT INTO `operation_logs` VALUES (819, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:19:49');
INSERT INTO `operation_logs` VALUES (820, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:38');
INSERT INTO `operation_logs` VALUES (821, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:39');
INSERT INTO `operation_logs` VALUES (822, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:39');
INSERT INTO `operation_logs` VALUES (823, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:39');
INSERT INTO `operation_logs` VALUES (824, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:41');
INSERT INTO `operation_logs` VALUES (825, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:41');
INSERT INTO `operation_logs` VALUES (826, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:41');
INSERT INTO `operation_logs` VALUES (827, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:20:41');
INSERT INTO `operation_logs` VALUES (828, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-08-02 17:22:06');
INSERT INTO `operation_logs` VALUES (829, '卡密验证', '用户验证卡密', '::1', '1841DB6F0EA2AB06', NULL, '2025-08-02 17:31:09');
INSERT INTO `operation_logs` VALUES (830, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:31:19');
INSERT INTO `operation_logs` VALUES (831, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:31:19');
INSERT INTO `operation_logs` VALUES (832, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:31:20');
INSERT INTO `operation_logs` VALUES (833, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:31:20');
INSERT INTO `operation_logs` VALUES (834, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:36:28');
INSERT INTO `operation_logs` VALUES (835, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:36:28');
INSERT INTO `operation_logs` VALUES (836, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:37:36');
INSERT INTO `operation_logs` VALUES (837, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:37:36');
INSERT INTO `operation_logs` VALUES (838, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:24');
INSERT INTO `operation_logs` VALUES (839, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:24');
INSERT INTO `operation_logs` VALUES (840, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:24');
INSERT INTO `operation_logs` VALUES (841, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:24');
INSERT INTO `operation_logs` VALUES (842, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:24');
INSERT INTO `operation_logs` VALUES (843, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:25');
INSERT INTO `operation_logs` VALUES (844, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:25');
INSERT INTO `operation_logs` VALUES (845, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:38:25');
INSERT INTO `operation_logs` VALUES (846, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:40:00');
INSERT INTO `operation_logs` VALUES (847, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:40:00');
INSERT INTO `operation_logs` VALUES (848, '提取文件', '用户提取文件资料', '::1', '1841DB6F0EA2AB06', 41, '2025-08-02 17:40:04');
INSERT INTO `operation_logs` VALUES (849, '卡密验证', '用户验证卡密', '::1', '21341D95A183E458', NULL, '2025-08-02 17:40:59');
INSERT INTO `operation_logs` VALUES (850, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:42:13');
INSERT INTO `operation_logs` VALUES (851, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 17:42:13');
INSERT INTO `operation_logs` VALUES (852, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-08-02 17:52:57');
INSERT INTO `operation_logs` VALUES (853, '卡密验证', '用户验证卡密', '::1', '21341D95A183E458', NULL, '2025-08-02 17:57:07');
INSERT INTO `operation_logs` VALUES (854, '卡密验证', '用户验证卡密', '::1', '21341D95A183E458', NULL, '2025-08-02 18:06:35');
INSERT INTO `operation_logs` VALUES (855, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:06:42');
INSERT INTO `operation_logs` VALUES (856, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:06:42');
INSERT INTO `operation_logs` VALUES (857, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:11:39');
INSERT INTO `operation_logs` VALUES (858, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:11:39');
INSERT INTO `operation_logs` VALUES (859, '管理员登录', '管理员账户登录', '::1', NULL, NULL, '2025-08-02 18:11:40');
INSERT INTO `operation_logs` VALUES (860, '卡密验证', '用户验证卡密', '::1', '16DF1CD64BBD4804', NULL, '2025-08-02 18:11:57');
INSERT INTO `operation_logs` VALUES (861, '卡密验证', '用户验证卡密', '::1', '16DF1CD64BBD4804', NULL, '2025-08-02 18:15:13');
INSERT INTO `operation_logs` VALUES (862, '清理无效卡密', '管理员清理无效卡密', '::1', NULL, NULL, '2025-08-02 18:15:38');
INSERT INTO `operation_logs` VALUES (863, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:16:11');
INSERT INTO `operation_logs` VALUES (864, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:16:11');
INSERT INTO `operation_logs` VALUES (865, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:23:20');
INSERT INTO `operation_logs` VALUES (866, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:23:20');
INSERT INTO `operation_logs` VALUES (867, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:24:31');
INSERT INTO `operation_logs` VALUES (868, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:24:31');
INSERT INTO `operation_logs` VALUES (869, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:26:55');
INSERT INTO `operation_logs` VALUES (870, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:26:55');
INSERT INTO `operation_logs` VALUES (871, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:30:08');
INSERT INTO `operation_logs` VALUES (872, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:30:08');
INSERT INTO `operation_logs` VALUES (873, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:31:01');
INSERT INTO `operation_logs` VALUES (874, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:31:01');
INSERT INTO `operation_logs` VALUES (875, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:32:04');
INSERT INTO `operation_logs` VALUES (876, '令牌检查', '检查令牌有效性和重用状态', '::1', NULL, NULL, '2025-08-02 18:32:04');

SET FOREIGN_KEY_CHECKS = 1;
