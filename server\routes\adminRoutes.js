const express = require('express');
const router = express.Router();
const adminFileController = require('../controllers/adminFileController');
const cardKeyController = require('../controllers/cardKeyController');
const categoryController = require('../controllers/categoryController');
const statsController = require('../controllers/statsController');
const authController = require('../controllers/authController');
const xianyuController = require('../controllers/xianyuController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/auth');
const { logOperation } = require('../middlewares/logger');

// 路由中间件 - 验证管理员身份
router.use(verifyAdminToken);

// 管理员密码相关路由
router.post('/change-password', logOperation('修改密码', '管理员修改密码'), authController.changeAdminPassword);

// 文件相关路由
router.get('/files', adminFileController.getAdminFiles);
router.post('/files/upload', adminFileController.uploadFile);
router.get('/files/:id', adminFileController.getAdminFileDetails);
router.put('/files/:id', adminFileController.updateFile);
router.delete('/files/:id', adminFileController.deleteFile);

// 简介图片访问路由
router.get('/preview/:filename', adminFileController.getPreviewImage);

// 卡密相关路由
router.get('/card-keys', cardKeyController.getCardKeys);
router.post('/card-keys/generate', cardKeyController.generateKeys);
router.post('/card-keys/batch-update-expire', cardKeyController.batchUpdateExpireTime);
router.put('/card-keys/:id/status', cardKeyController.updateCardKeyStatus);
router.delete('/card-keys/:id', cardKeyController.deleteCardKey);
router.post('/card-keys/batch-delete', cardKeyController.batchDeleteCardKeys);
router.post('/card-keys/cleanup-expired', logOperation('清理过期卡密', '管理员清理过期卡密'), cardKeyController.manualCleanupExpiredCardKeys);
router.post('/card-keys/cleanup-used', logOperation('清理已使用卡密', '管理员清理已使用卡密'), cardKeyController.manualCleanupUsedCardKeys);
router.post('/card-keys/cleanup-invalid', logOperation('清理无效卡密', '管理员清理无效卡密'), cardKeyController.manualCleanupInvalidCardKeys);

// 分类相关路由
router.get('/categories', categoryController.getCategories);
router.post('/categories', logOperation('创建分类', '管理员创建分类'), categoryController.createCategory);
router.get('/categories/:id', categoryController.getCategoryById);
router.put('/categories/:id', logOperation('更新分类', '管理员更新分类'), categoryController.updateCategory);
router.delete('/categories/:id', logOperation('删除分类', '管理员删除分类'), categoryController.deleteCategory);

// 提取记录相关路由
router.get('/extraction-records', statsController.getExtractionRecords);
router.delete('/extraction-records/:id', logOperation('删除提取记录', '管理员删除提取记录'), statsController.deleteExtractionRecord);
router.post('/extraction-records/batch-delete', logOperation('批量删除提取记录', '管理员批量删除提取记录'), statsController.batchDeleteExtractionRecords);
router.get('/stats', statsController.getStatsData);

// 数据分析相关路由
router.get('/analytics/popular-files', statsController.getPopularFiles);
router.get('/analytics/detailed', statsController.getDetailedAnalytics);

// 闲鱼商品相关路由
router.post('/xianyu/sync', logOperation('同步闲鱼商品', '管理员同步闲鱼商品'), xianyuController.syncXianyuItems);
router.get('/xianyu/items', xianyuController.getXianyuItems);
router.delete('/xianyu/items/:id', logOperation('删除闲鱼商品', '管理员删除闲鱼商品'), xianyuController.deleteXianyuItem);

module.exports = router;