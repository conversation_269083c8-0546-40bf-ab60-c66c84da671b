#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品同步脚本
用于从闲鱼获取商品信息并返回JSON格式数据
"""

import sys
import json
import os
import time
from pathlib import Path

# 添加XianYu目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
xianyu_dir = project_root / "XianYu"
sys.path.insert(0, str(xianyu_dir))

try:
    from xianyu_client import XianyuClient
except ImportError as e:
    # 如果导入失败，尝试使用简化版本
    try:
        from xianyu_client_simple import XianyuClientSimple as XianyuClient
    except ImportError:
        print(json.dumps({
            'success': False,
            'message': f'导入闲鱼客户端失败: {str(e)}。请安装必要的Python依赖：pip install execjs blackboxprotobuf',
            'data': None
        }))
        sys.exit(1)


def sync_xianyu_items(cookies_str, max_items=0, get_details=True):
    """
    同步闲鱼商品信息
    
    Args:
        cookies_str: 闲鱼Cookie字符串
        max_items: 最大获取数量，0表示全部
        get_details: 是否获取详细信息
        
    Returns:
        包含商品信息的字典
    """
    try:
        # 创建闲鱼客户端
        client = XianyuClient(cookies_str)
        
        # 获取用户信息
        user_info = client.get_user_info()
        
        # 定义进度回调函数
        def progress_callback(current, total, item):
            progress_data = {
                'type': 'progress',
                'current': current,
                'total': total,
                'item_title': item.get('title', ''),
                'item_id': item.get('itemId', '')
            }
            print(json.dumps(progress_data))
        
        # 获取商品信息
        items = client.get_all_items(
            max_items=max_items,
            get_details=get_details,
            delay=1.0,  # 1秒延迟避免请求过快
            progress_callback=progress_callback
        )
        
        # 处理商品数据，转换为适合files表的格式
        processed_items = []
        for item in items:
            # 处理图片列表
            images = item.get('detailImages', item.get('images', []))
            if isinstance(images, list):
                # 只保留前5张图片，避免数据过大
                images = images[:5]
            else:
                images = []
            
            processed_item = {
                'xianyu_item_id': item.get('itemId', ''),
                'file_name': item.get('title', ''),
                'description': item.get('fullDescription', item.get('description', '')),
                'preview_images': json.dumps(images) if images else None,
                'file_type': 'xianyu',
                'file_path': None,
                'file_size': None,
                'important_text': item.get('itemId', ''),  # 将闲鱼商品ID存储在这里
                'status': 1,
                'category_id': None,
                # 保留原始闲鱼数据用于调试
                'original_data': {
                    'price': item.get('price', ''),
                    'originalPrice': item.get('originalPrice', ''),
                    'status': item.get('status', ''),
                    'statusText': item.get('statusText', ''),
                    'viewCount': item.get('viewCount', 0),
                    'likeCount': item.get('likeCount', 0),
                    'publishTime': item.get('publishTime', ''),
                    'location': item.get('location', ''),
                    'sellerId': item.get('sellerId', ''),
                    'sellerNick': item.get('sellerNick', '')
                }
            }
            processed_items.append(processed_item)
        
        return {
            'success': True,
            'message': f'成功获取 {len(processed_items)} 个商品',
            'data': {
                'user_info': user_info,
                'items': processed_items,
                'total': len(processed_items)
            }
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'同步失败: {str(e)}',
            'data': None
        }


def main():
    """主函数"""
    try:
        # 从命令行参数获取配置
        if len(sys.argv) < 2:
            print(json.dumps({
                'success': False,
                'message': '缺少必要参数',
                'data': None
            }))
            sys.exit(1)
        
        # 解析参数
        config = json.loads(sys.argv[1])
        cookies_str = config.get('cookies', '')
        max_items = config.get('maxItems', 0)
        get_details = config.get('getDetails', True)
        
        if not cookies_str:
            print(json.dumps({
                'success': False,
                'message': 'Cookie不能为空',
                'data': None
            }))
            sys.exit(1)
        
        # 执行同步
        result = sync_xianyu_items(cookies_str, max_items, get_details)
        
        # 输出结果
        print(json.dumps(result, ensure_ascii=False))
        
    except json.JSONDecodeError:
        print(json.dumps({
            'success': False,
            'message': '参数格式错误',
            'data': None
        }))
        sys.exit(1)
    except Exception as e:
        print(json.dumps({
            'success': False,
            'message': f'脚本执行错误: {str(e)}',
            'data': None
        }))
        sys.exit(1)


if __name__ == '__main__':
    main()
