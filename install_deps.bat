@echo off
echo ========================================
echo 闲鱼功能依赖安装脚本
echo ========================================
echo.

echo 正在检测Python环境...
echo.

REM 尝试不同的Python命令
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 找到Python命令: python
    echo 正在安装依赖包...
    python -m pip install execjs blackboxprotobuf
    if %errorlevel% == 0 (
        echo.
        echo ✅ 依赖安装成功！
        goto :success
    ) else (
        echo ❌ 使用python命令安装失败，尝试其他方法...
    )
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo 找到Python命令: python3
    echo 正在安装依赖包...
    python3 -m pip install execjs blackboxprotobuf
    if %errorlevel% == 0 (
        echo.
        echo ✅ 依赖安装成功！
        goto :success
    ) else (
        echo ❌ 使用python3命令安装失败...
    )
)

REM 尝试使用完整路径
if exist "E:\Develop Tool\Python\Python3.13\python.exe" (
    echo 找到Python: E:\Develop Tool\Python\Python3.13\python.exe
    echo 正在安装依赖包...
    "E:\Develop Tool\Python\Python3.13\python.exe" -m pip install execjs blackboxprotobuf
    if %errorlevel% == 0 (
        echo.
        echo ✅ 依赖安装成功！
        goto :success
    ) else (
        echo ❌ 使用完整路径安装失败...
    )
)

echo.
echo ❌ 无法找到可用的Python环境或安装失败
echo.
echo 💡 解决方案：
echo 1. 确保Python已正确安装
echo 2. 检查Python是否添加到系统PATH
echo 3. 或者直接测试功能（会使用模拟数据）
echo.
echo 📝 手动安装命令：
echo    python -m pip install execjs blackboxprotobuf
echo    或
echo    python3 -m pip install execjs blackboxprotobuf
echo.
goto :end

:success
echo.
echo 🎉 安装完成！现在可以使用完整的闲鱼同步功能了。
echo.
echo 📋 接下来的步骤：
echo 1. 启动服务器
echo 2. 登录管理员后台
echo 3. 进入文件管理页面
echo 4. 点击"同步闲鱼商品"按钮
echo.

:end
echo 按任意键退出...
pause >nul
